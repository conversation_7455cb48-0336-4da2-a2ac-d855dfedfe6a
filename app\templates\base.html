<!DOCTYPE html>
<html lang="en" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Plex User Subscription Manager{% endblock %}</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/custom.css') }}">
    
    <!-- <PERSON><PERSON><PERSON> Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        plex: {
                            50: '#fef7ee',
                            100: '#fdedd3',
                            200: '#fbd7a5',
                            300: '#f8bb6d',
                            400: '#f59332',
                            500: '#f2750a',
                            600: '#e35d05',
                            700: '#bc4508',
                            800: '#95370e',
                            900: '#782f0f',
                        }
                    }
                }
            }
        }
    </script>
    
    {% block head %}{% endblock %}
</head>
<body class="h-full" x-data="{ sidebarOpen: false }">
    <div class="min-h-full">
        {% if current_user.is_authenticated %}
            <!-- Sidebar for mobile -->
            <div x-show="sidebarOpen" class="relative z-50 lg:hidden" x-cloak>
                <div x-show="sidebarOpen" x-transition:enter="transition-opacity ease-linear duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="transition-opacity ease-linear duration-300" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="fixed inset-0 bg-gray-900/80"></div>
                
                <div class="fixed inset-0 flex">
                    <div x-show="sidebarOpen" x-transition:enter="transition ease-in-out duration-300 transform" x-transition:enter-start="-translate-x-full" x-transition:enter-end="translate-x-0" x-transition:leave="transition ease-in-out duration-300 transform" x-transition:leave-start="translate-x-0" x-transition:leave-end="-translate-x-full" class="relative mr-16 flex w-full max-w-xs flex-1">
                        <div x-show="sidebarOpen" x-transition:enter="ease-in-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="ease-in-out duration-300" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="absolute left-full top-0 flex w-16 justify-center pt-5">
                            <button type="button" class="-m-2.5 p-2.5" @click="sidebarOpen = false">
                                <span class="sr-only">Close sidebar</span>
                                <i class="fas fa-times h-6 w-6 text-white"></i>
                            </button>
                        </div>
                        {% include 'components/sidebar.html' %}
                    </div>
                </div>
            </div>

            <!-- Static sidebar for desktop -->
            <div class="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col">
                {% include 'components/sidebar.html' %}
            </div>

            <!-- Main content -->
            <div class="lg:pl-72">
                <!-- Top navigation -->
                <div class="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 border-b border-gray-200 bg-white px-4 shadow-sm sm:gap-x-6 sm:px-6 lg:px-8">
                    <button type="button" class="-m-2.5 p-2.5 text-gray-700 lg:hidden" @click="sidebarOpen = true">
                        <span class="sr-only">Open sidebar</span>
                        <i class="fas fa-bars h-5 w-5"></i>
                    </button>

                    <!-- Separator -->
                    <div class="h-6 w-px bg-gray-200 lg:hidden"></div>

                    <div class="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
                        <div class="relative flex flex-1 items-center">
                            <h1 class="text-lg font-semibold text-gray-900">
                                {% block page_title %}Dashboard{% endblock %}
                            </h1>
                        </div>
                        <div class="flex items-center gap-x-4 lg:gap-x-6">
                            <!-- Notifications -->
                            <button type="button" class="-m-2.5 p-2.5 text-gray-400 hover:text-gray-500" x-data="{ hasNotifications: true }">
                                <span class="sr-only">View notifications</span>
                                <i class="fas fa-bell h-6 w-6"></i>
                                <span x-show="hasNotifications" class="absolute -mt-2 -mr-1 h-2 w-2 rounded-full bg-red-500"></span>
                            </button>

                            <!-- Profile dropdown -->
                            <div class="relative" x-data="{ open: false }">
                                <button type="button" class="-m-1.5 flex items-center p-1.5" @click="open = !open">
                                    <span class="sr-only">Open user menu</span>
                                    <div class="h-8 w-8 rounded-full bg-plex-500 flex items-center justify-center">
                                        <span class="text-sm font-medium text-white">{{ current_user.username[0].upper() }}</span>
                                    </div>
                                    <span class="hidden lg:flex lg:items-center">
                                        <span class="ml-4 text-sm font-semibold leading-6 text-gray-900">{{ current_user.username }}</span>
                                        <i class="fas fa-chevron-down ml-2 h-5 w-5 text-gray-400"></i>
                                    </span>
                                </button>
                                
                                <div x-show="open" @click.away="open = false" x-transition:enter="transition ease-out duration-100" x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="transition ease-in duration-75" x-transition:leave-start="transform opacity-100 scale-100" x-transition:leave-end="transform opacity-0 scale-95" class="absolute right-0 z-10 mt-2.5 w-32 origin-top-right rounded-md bg-white py-2 shadow-lg ring-1 ring-gray-900/5" x-cloak>
                                    <a href="{{ url_for('auth.change_password') }}" class="block px-3 py-1 text-sm leading-6 text-gray-900 hover:bg-gray-50">Change Password</a>
                                    <a href="{{ url_for('auth.logout') }}" class="block px-3 py-1 text-sm leading-6 text-gray-900 hover:bg-gray-50">Sign out</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Page content -->
                <main class="py-10">
                    <div class="px-4 sm:px-6 lg:px-8">
                        {% include 'components/flash_messages.html' %}
                        {% block content %}{% endblock %}
                    </div>
                </main>
            </div>
        {% else %}
            <!-- Not authenticated layout -->
            <main class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
                {% include 'components/flash_messages.html' %}
                {% block content %}{% endblock %}
            </main>
        {% endif %}
    </div>

    <!-- Loading overlay -->
    <div x-data="{ loading: false }" x-show="loading" x-cloak class="fixed inset-0 z-50 flex items-center justify-center bg-gray-900 bg-opacity-50">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-4">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-plex-500"></div>
            <span class="text-gray-700">Loading...</span>
        </div>
    </div>

    <!-- Custom JavaScript -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
