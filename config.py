import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///plex_subscription.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Plex OAuth Configuration
    PLEX_CLIENT_ID = os.environ.get('PLEX_CLIENT_ID')
    PLEX_CLIENT_SECRET = os.environ.get('PLEX_CLIENT_SECRET')
    PLEX_REDIRECT_URI = os.environ.get('PLEX_REDIRECT_URI') or 'http://localhost:5000/auth/plex/callback'
    
    # Plex API Configuration
    PLEX_API_BASE_URL = 'https://plex.tv/api/v2'
    PLEX_AUTH_URL = 'https://app.plex.tv/auth'
    
    # Application Configuration
    ITEMS_PER_PAGE = 20
    SESSION_TIMEOUT = 3600  # 1 hour
    
    # Security Configuration
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = None

class DevelopmentConfig(Config):
    DEBUG = True
    SQLALCHEMY_ECHO = True

class ProductionConfig(Config):
    DEBUG = False
    SQLALCHEMY_ECHO = False

class TestingConfig(Config):
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False

config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
