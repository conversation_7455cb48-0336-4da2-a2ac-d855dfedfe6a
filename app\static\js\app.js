// Main JavaScript application for Plex User Subscription Manager

// Global app configuration
window.PlexApp = {
    config: {
        apiBaseUrl: '/api',
        refreshInterval: 30000, // 30 seconds
        countdownInterval: 1000 // 1 second
    },
    
    // Utility functions
    utils: {
        // Format date for display
        formatDate: function(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
        },
        
        // Format time remaining
        formatTimeRemaining: function(seconds) {
            const days = Math.floor(seconds / 86400);
            const hours = Math.floor((seconds % 86400) / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            
            if (days > 0) {
                return `${days}d ${hours}h ${minutes}m`;
            } else if (hours > 0) {
                return `${hours}h ${minutes}m`;
            } else {
                return `${minutes}m`;
            }
        },
        
        // Show notification
        showNotification: function(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type === 'error' ? 'bg-red-50 border-red-200' : type === 'success' ? 'bg-green-50 border-green-200' : 'bg-blue-50 border-blue-200'}`;
            notification.innerHTML = `
                <div class="p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas ${type === 'error' ? 'fa-exclamation-circle text-red-400' : type === 'success' ? 'fa-check-circle text-green-400' : 'fa-info-circle text-blue-400'} h-5 w-5"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium ${type === 'error' ? 'text-red-800' : type === 'success' ? 'text-green-800' : 'text-blue-800'}">${message}</p>
                        </div>
                        <div class="ml-auto pl-3">
                            <button type="button" onclick="this.parentElement.parentElement.parentElement.parentElement.remove()" class="inline-flex rounded-md p-1.5 ${type === 'error' ? 'text-red-500 hover:bg-red-100' : type === 'success' ? 'text-green-500 hover:bg-green-100' : 'text-blue-500 hover:bg-blue-100'}">
                                <i class="fas fa-times h-5 w-5"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        },
        
        // Make API request
        apiRequest: async function(url, options = {}) {
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            };
            
            const mergedOptions = { ...defaultOptions, ...options };
            if (mergedOptions.body && typeof mergedOptions.body === 'object') {
                mergedOptions.body = JSON.stringify(mergedOptions.body);
            }
            
            try {
                const response = await fetch(url, mergedOptions);
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.message || `HTTP error! status: ${response.status}`);
                }
                
                return data;
            } catch (error) {
                console.error('API request failed:', error);
                throw error;
            }
        }
    },
    
    // Subscription management functions
    subscription: {
        // Extend user subscription
        extend: async function(userId, days) {
            try {
                const data = await PlexApp.utils.apiRequest(`/api/users/${userId}/extend-subscription`, {
                    method: 'POST',
                    body: { days: parseInt(days) }
                });
                
                PlexApp.utils.showNotification(data.message, 'success');
                
                // Refresh the page or update UI
                if (typeof window.refreshUserData === 'function') {
                    window.refreshUserData(userId);
                } else {
                    location.reload();
                }
                
                return data;
            } catch (error) {
                PlexApp.utils.showNotification(error.message, 'error');
                throw error;
            }
        },
        
        // Cancel user subscription
        cancel: async function(userId) {
            if (!confirm('Are you sure you want to cancel this subscription?')) {
                return;
            }
            
            try {
                const data = await PlexApp.utils.apiRequest(`/api/users/${userId}/cancel-subscription`, {
                    method: 'POST'
                });
                
                PlexApp.utils.showNotification(data.message, 'success');
                
                // Refresh the page or update UI
                if (typeof window.refreshUserData === 'function') {
                    window.refreshUserData(userId);
                } else {
                    location.reload();
                }
                
                return data;
            } catch (error) {
                PlexApp.utils.showNotification(error.message, 'error');
                throw error;
            }
        },
        
        // Create new subscription
        create: async function(userId, days, planName = '') {
            try {
                const data = await PlexApp.utils.apiRequest(`/api/users/${userId}/create-subscription`, {
                    method: 'POST',
                    body: { days: parseInt(days), plan_name: planName }
                });
                
                PlexApp.utils.showNotification(data.message, 'success');
                
                // Refresh the page or update UI
                if (typeof window.refreshUserData === 'function') {
                    window.refreshUserData(userId);
                } else {
                    location.reload();
                }
                
                return data;
            } catch (error) {
                PlexApp.utils.showNotification(error.message, 'error');
                throw error;
            }
        }
    },
    
    // Library sharing functions
    library: {
        // Share library with user
        share: async function(userId, libraryId) {
            try {
                const data = await PlexApp.utils.apiRequest(`/api/users/${userId}/share-library`, {
                    method: 'POST',
                    body: { library_id: parseInt(libraryId) }
                });
                
                PlexApp.utils.showNotification(data.message, 'success');
                return data;
            } catch (error) {
                PlexApp.utils.showNotification(error.message, 'error');
                throw error;
            }
        },
        
        // Unshare library from user
        unshare: async function(userId, libraryId) {
            try {
                const data = await PlexApp.utils.apiRequest(`/api/users/${userId}/unshare-library`, {
                    method: 'POST',
                    body: { library_id: parseInt(libraryId) }
                });
                
                PlexApp.utils.showNotification(data.message, 'success');
                return data;
            } catch (error) {
                PlexApp.utils.showNotification(error.message, 'error');
                throw error;
            }
        }
    },
    
    // Countdown timer functionality
    countdown: {
        timers: new Map(),
        
        // Start countdown for a user
        start: function(userId, endDate, elementId) {
            // Clear existing timer if any
            if (this.timers.has(userId)) {
                clearInterval(this.timers.get(userId));
            }
            
            const element = document.getElementById(elementId);
            if (!element) return;
            
            const timer = setInterval(() => {
                const now = new Date().getTime();
                const end = new Date(endDate).getTime();
                const timeLeft = end - now;
                
                if (timeLeft <= 0) {
                    element.innerHTML = '<span class="text-red-600 font-semibold">Expired</span>';
                    clearInterval(timer);
                    this.timers.delete(userId);
                    return;
                }
                
                const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
                const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
                
                let display = '';
                if (days > 0) {
                    display = `${days}d ${hours}h ${minutes}m`;
                } else if (hours > 0) {
                    display = `${hours}h ${minutes}m ${seconds}s`;
                } else {
                    display = `${minutes}m ${seconds}s`;
                }
                
                // Color coding based on time left
                let colorClass = 'text-green-600';
                if (days <= 1) {
                    colorClass = 'text-red-600';
                } else if (days <= 7) {
                    colorClass = 'text-yellow-600';
                }
                
                element.innerHTML = `<span class="${colorClass} font-semibold countdown-timer">${display}</span>`;
            }, 1000);
            
            this.timers.set(userId, timer);
        },
        
        // Stop countdown for a user
        stop: function(userId) {
            if (this.timers.has(userId)) {
                clearInterval(this.timers.get(userId));
                this.timers.delete(userId);
            }
        },
        
        // Stop all countdowns
        stopAll: function() {
            this.timers.forEach((timer) => clearInterval(timer));
            this.timers.clear();
        }
    },
    
    // Initialize the application
    init: function() {
        console.log('Plex User Subscription Manager initialized');
        
        // Set up global error handling
        window.addEventListener('error', function(e) {
            console.error('Global error:', e.error);
        });
        
        // Set up AJAX error handling
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-start countdowns for elements with data-countdown attribute
            document.querySelectorAll('[data-countdown]').forEach(element => {
                const userId = element.dataset.userId;
                const endDate = element.dataset.endDate;
                if (userId && endDate) {
                    PlexApp.countdown.start(userId, endDate, element.id);
                }
            });
        });
        
        // Clean up on page unload
        window.addEventListener('beforeunload', function() {
            PlexApp.countdown.stopAll();
        });
    }
};

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    PlexApp.init();
});

// Export for use in other scripts
window.PlexApp = PlexApp;
