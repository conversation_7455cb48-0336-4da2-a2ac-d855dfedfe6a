"""
Admin routes for settings, Plex configuration, and admin management
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, session
from flask_login import login_required, current_user
from functools import wraps
from app import db
from app.models import Admin, PlexServer, Library
from app.services.plex_service import PlexService, PlexAPIError

admin_bp = Blueprint('admin', __name__)

def master_admin_required(f):
    """Decorator to require master admin privileges"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_master:
            flash('Master admin privileges required.', 'error')
            return redirect(url_for('main.dashboard'))
        return f(*args, **kwargs)
    return decorated_function

@admin_bp.route('/settings')
@login_required
def settings():
    """Admin settings page"""
    plex_server = current_user.get_plex_server()
    return render_template('admin/settings.html', plex_server=plex_server)

@admin_bp.route('/plex-setup', methods=['GET', 'POST'])
@login_required
def plex_setup():
    """Plex server setup page"""
    plex_server = current_user.get_plex_server()
    
    if request.method == 'POST':
        server_name = request.form.get('server_name')
        server_url = request.form.get('server_url')
        token = request.form.get('token') or session.get('plex_token')
        
        if not all([server_name, server_url, token]):
            flash('All fields are required.', 'error')
            return render_template('admin/plex_setup.html', plex_server=plex_server)
        
        # Test connection
        try:
            plex_service = PlexService()
            success, message = plex_service.test_connection(server_url, token)
            
            if not success:
                flash(f'Connection test failed: {message}', 'error')
                return render_template('admin/plex_setup.html', plex_server=plex_server)
            
            # Get server info
            server_info = plex_service.get_server_info(server_url, token)
            
            if plex_server:
                # Update existing server
                plex_server.name = server_name
                plex_server.url = server_url
                plex_server.set_token(token)
                plex_server.machine_identifier = server_info.get('machineIdentifier')
                plex_server.version = server_info.get('version')
                flash('Plex server updated successfully.', 'success')
            else:
                # Create new server
                plex_server = PlexServer(
                    admin_id=current_user.id,
                    name=server_name,
                    url=server_url,
                    token=token
                )
                plex_server.machine_identifier = server_info.get('machineIdentifier')
                plex_server.version = server_info.get('version')
                db.session.add(plex_server)
                flash('Plex server configured successfully.', 'success')
            
            db.session.commit()
            
            # Clear token from session
            session.pop('plex_token', None)
            
            # Sync libraries
            try:
                plex_service_instance = PlexService(plex_server)
                lib_success, lib_message = plex_service_instance.sync_libraries_from_plex()
                if lib_success:
                    flash(f'Libraries synced: {lib_message}', 'info')
            except Exception as e:
                flash(f'Warning: Could not sync libraries: {str(e)}', 'warning')
            
            return redirect(url_for('admin.library_settings'))
            
        except Exception as e:
            flash(f'Setup failed: {str(e)}', 'error')
    
    # Pre-fill token from session if available
    session_token = session.get('plex_token')
    
    return render_template('admin/plex_setup.html', 
                         plex_server=plex_server,
                         session_token=session_token)

@admin_bp.route('/library-settings', methods=['GET', 'POST'])
@login_required
def library_settings():
    """Library sharing settings page"""
    plex_server = current_user.get_plex_server()
    if not plex_server:
        flash('Please configure your Plex server first.', 'warning')
        return redirect(url_for('admin.plex_setup'))
    
    libraries = Library.query.filter_by(plex_server_id=plex_server.id).all()
    
    if request.method == 'POST':
        # Get selected default libraries
        default_library_ids = request.form.getlist('default_libraries')
        
        # Update server default libraries
        plex_server.set_default_libraries(default_library_ids)
        
        # Update individual library settings
        for library in libraries:
            is_default = str(library.plex_library_id) in default_library_ids
            library.is_shared_by_default = is_default
        
        db.session.commit()
        flash('Library settings updated successfully.', 'success')
        return redirect(url_for('admin.library_settings'))
    
    # Get current default libraries
    default_library_ids = plex_server.get_default_libraries()
    
    return render_template('admin/library_settings.html', 
                         libraries=libraries,
                         default_library_ids=default_library_ids)

@admin_bp.route('/manage-admins')
@master_admin_required
def manage_admins():
    """Manage admin users (master admin only)"""
    admins = Admin.query.all()
    return render_template('admin/manage_admins.html', admins=admins)

@admin_bp.route('/create-admin', methods=['GET', 'POST'])
@master_admin_required
def create_admin():
    """Create new admin user (master admin only)"""
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        
        # Validation
        if not all([username, email, password, confirm_password]):
            flash('All fields are required.', 'error')
            return render_template('admin/create_admin.html')
        
        if password != confirm_password:
            flash('Passwords do not match.', 'error')
            return render_template('admin/create_admin.html')
        
        if len(password) < 6:
            flash('Password must be at least 6 characters long.', 'error')
            return render_template('admin/create_admin.html')
        
        # Check if username or email already exists
        if Admin.query.filter_by(username=username).first():
            flash('Username already exists.', 'error')
            return render_template('admin/create_admin.html')
        
        if Admin.query.filter_by(email=email).first():
            flash('Email already exists.', 'error')
            return render_template('admin/create_admin.html')
        
        try:
            new_admin = Admin(
                username=username,
                email=email,
                password=password,
                is_master=False,
                created_by_id=current_user.id
            )
            
            db.session.add(new_admin)
            db.session.commit()
            
            flash(f'Admin account created successfully for {username}.', 'success')
            return redirect(url_for('admin.manage_admins'))
            
        except Exception as e:
            db.session.rollback()
            flash('An error occurred while creating the admin account.', 'error')
    
    return render_template('admin/create_admin.html')

@admin_bp.route('/toggle-admin/<int:admin_id>', methods=['POST'])
@master_admin_required
def toggle_admin_status(admin_id):
    """Toggle admin active status"""
    admin = Admin.query.get_or_404(admin_id)
    
    if admin.id == current_user.id:
        flash('You cannot deactivate your own account.', 'error')
        return redirect(url_for('admin.manage_admins'))
    
    if admin.is_master:
        flash('Cannot deactivate master admin accounts.', 'error')
        return redirect(url_for('admin.manage_admins'))
    
    admin.is_active = not admin.is_active
    db.session.commit()
    
    status = 'activated' if admin.is_active else 'deactivated'
    flash(f'Admin {admin.username} has been {status}.', 'success')
    
    return redirect(url_for('admin.manage_admins'))

@admin_bp.route('/api/sync-libraries', methods=['POST'])
@login_required
def api_sync_libraries():
    """API endpoint to sync libraries from Plex"""
    plex_server = current_user.get_plex_server()
    if not plex_server:
        return jsonify({'error': 'No Plex server configured'}), 400
    
    try:
        plex_service = PlexService(plex_server)
        success, message = plex_service.sync_libraries_from_plex()
        
        if success:
            return jsonify({'success': True, 'message': message})
        else:
            return jsonify({'success': False, 'message': message}), 500
            
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@admin_bp.route('/api/test-plex-token', methods=['POST'])
@login_required
def api_test_plex_token():
    """API endpoint to test Plex token"""
    data = request.get_json()
    token = data.get('token')
    
    plex_server = current_user.get_plex_server()
    if not plex_server:
        return jsonify({'error': 'No Plex server configured'}), 400
    
    if not token:
        return jsonify({'error': 'Token is required'}), 400
    
    try:
        plex_service = PlexService()
        success, message = plex_service.test_connection(plex_server.url, token)
        
        return jsonify({'success': success, 'message': message})
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@admin_bp.route('/api/update-plex-token', methods=['POST'])
@login_required
def api_update_plex_token():
    """API endpoint to update Plex token"""
    data = request.get_json()
    token = data.get('token')
    
    plex_server = current_user.get_plex_server()
    if not plex_server:
        return jsonify({'error': 'No Plex server configured'}), 400
    
    if not token:
        return jsonify({'error': 'Token is required'}), 400
    
    try:
        # Test token first
        plex_service = PlexService()
        success, message = plex_service.test_connection(plex_server.url, token)
        
        if not success:
            return jsonify({'success': False, 'message': f'Token test failed: {message}'}), 400
        
        # Update token
        plex_server.set_token(token)
        db.session.commit()
        
        return jsonify({'success': True, 'message': 'Plex token updated successfully'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': str(e)}), 500

@admin_bp.route('/api/library-stats')
@login_required
def api_library_stats():
    """API endpoint for library statistics"""
    plex_server = current_user.get_plex_server()
    if not plex_server:
        return jsonify({'error': 'No Plex server configured'}), 400
    
    libraries = Library.query.filter_by(plex_server_id=plex_server.id).all()
    
    stats = []
    for library in libraries:
        shared_count = library.get_shared_users_count()
        total_users = db.session.query(db.func.count(db.distinct(User.id))).filter_by(plex_server_id=plex_server.id).scalar()
        
        stats.append({
            'id': library.id,
            'title': library.title,
            'type': library.type,
            'shared_count': shared_count,
            'total_users': total_users,
            'share_percentage': round((shared_count / total_users * 100) if total_users > 0 else 0, 1)
        })
    
    return jsonify(stats)
