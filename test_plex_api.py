#!/usr/bin/env python3
"""
Test script for Plex API sharing/unsharing based on the GitHub gist
https://gist.github.com/JonnyWong16/f8139216e2748cb367558070c1448636
"""

import requests
import json
from xml.dom import minidom

# Configuration
PLEX_TOKEN = "KfQs3Gi6pfzbe4dJTHMU"
SERVER_ID = "8e7d7fd0bfe3b8bad6af94e6c28396aa4bfa1d15"

def get_shared_servers():
    """Get current shared servers to see the structure"""
    headers = {"X-Plex-Token": PLEX_TOKEN, "Accept": "application/json"}
    url = f"https://plex.tv/api/servers/{SERVER_ID}/shared_servers"
    
    print(f"Getting shared servers from: {url}")
    r = requests.get(url, headers=headers)
    
    print(f"Status code: {r.status_code}")
    if r.status_code == 200:
        print("Response content:")
        print(r.text)
        return r.text
    else:
        print(f"Error: {r.text}")
        return None

def test_share_library(user_id, library_ids):
    """Test sharing libraries with a user - this updates existing shares"""
    headers = {"X-Plex-Token": PLEX_TOKEN, "Accept": "application/json"}
    url = f"https://plex.tv/api/servers/{SERVER_ID}/shared_servers"

    # First get current libraries for this user
    current_libs = get_user_current_libraries(user_id)
    if current_libs is None:
        print(f"User {user_id} not found in shared servers")
        return False

    # Add the new library to existing ones
    all_libraries = list(set(current_libs + library_ids))

    payload = {
        "server_id": SERVER_ID,
        "shared_server": {
            "library_section_ids": all_libraries,
            "invited_id": user_id
        }
    }

    print(f"\nTesting SHARE for user {user_id}")
    print(f"Current libraries: {current_libs}")
    print(f"Adding libraries: {library_ids}")
    print(f"Final libraries: {all_libraries}")
    print(f"URL: {url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")

    r = requests.post(url, headers=headers, json=payload)

    print(f"Status code: {r.status_code}")
    print(f"Response: {r.text}")

    return r.status_code == 200

def get_user_current_libraries(user_id):
    """Get current shared libraries for a user"""
    headers = {"X-Plex-Token": PLEX_TOKEN, "Accept": "application/json"}
    url = f"https://plex.tv/api/servers/{SERVER_ID}/shared_servers"

    r = requests.get(url, headers=headers)
    if r.status_code != 200:
        return None

    try:
        response_xml = minidom.parseString(r.content)
        MediaContainer = response_xml.getElementsByTagName("MediaContainer")[0]
        SharedServer = MediaContainer.getElementsByTagName("SharedServer")

        for s in SharedServer:
            if int(s.getAttribute("userID")) == user_id:
                # Get all shared libraries (shared="1")
                sections = s.getElementsByTagName("Section")
                shared_libs = []
                for section in sections:
                    if section.getAttribute("shared") == "1":
                        shared_libs.append(int(section.getAttribute("key")))
                return shared_libs

        return None
    except Exception as e:
        print(f"Error parsing XML: {e}")
        return None

def test_unshare_library(user_id):
    """Test unsharing all libraries from a user"""
    headers = {"X-Plex-Token": PLEX_TOKEN, "Accept": "application/json"}
    
    # First get the shared server ID for this user
    url = f"https://plex.tv/api/servers/{SERVER_ID}/shared_servers"
    r = requests.get(url, headers=headers)
    
    if r.status_code != 200:
        print(f"Error getting shared servers: {r.text}")
        return False
    
    # Parse XML to find the shared server ID
    try:
        response_xml = minidom.parseString(r.content)
        MediaContainer = response_xml.getElementsByTagName("MediaContainer")[0]
        SharedServer = MediaContainer.getElementsByTagName("SharedServer")
        
        shared_server_id = None
        for s in SharedServer:
            if int(s.getAttribute("userID")) == user_id:
                shared_server_id = int(s.getAttribute("id"))
                break
        
        if not shared_server_id:
            print(f"No shared server found for user {user_id}")
            return False
        
        print(f"\nTesting UNSHARE for user {user_id} (shared_server_id: {shared_server_id})")
        
        # Delete the shared server
        delete_url = f"https://plex.tv/api/servers/{SERVER_ID}/shared_servers/{shared_server_id}"
        print(f"DELETE URL: {delete_url}")
        
        r = requests.delete(delete_url, headers=headers)
        
        print(f"Status code: {r.status_code}")
        print(f"Response: {r.text}")
        
        return r.status_code == 200
        
    except Exception as e:
        print(f"Error parsing XML: {e}")
        return False

if __name__ == "__main__":
    print("=== Plex API Sharing Test ===")
    
    # First, get current shared servers
    print("\n1. Getting current shared servers:")
    get_shared_servers()
    
    # Test with a real user from our system
    # User ID 3677763 (eglep) and library ID 16 (Animation TVS) - eglep has allLibraries="0"
    test_user_id = 3677763  # eglep from the XML response
    test_library_ids = [16]  # Animation (TVS) library (currently shared="0" with eglep)
    
    print(f"\n2. Testing with User ID: {test_user_id}, Library IDs: {test_library_ids}")
    
    # Skip sharing test for now and test unsharing directly
    print("\n⚠️ Skipping share test due to 404 errors")
    print("Testing unshare functionality instead...")

    # Test unsharing - this should work since the user exists
    unshare_success = test_unshare_library(test_user_id)

    if unshare_success:
        print("\n✅ Unshare test successful!")
        print("This means the API endpoints work, but sharing might need different parameters")
    else:
        print("\n❌ Unshare test failed!")
    
    # Show final state
    print("\n3. Final shared servers state:")
    get_shared_servers()
