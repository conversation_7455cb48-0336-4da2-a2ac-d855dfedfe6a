"""
API routes for AJAX functionality and real-time updates
"""

from flask import Blueprint, request, jsonify
from flask_login import login_required, current_user
from datetime import datetime, timedelta
from app import db
from app.models import User, Subscription, Library, SharedLibrary
from app.services.subscription_service import SubscriptionService

api_bp = Blueprint('api', __name__)

@api_bp.route('/users/<int:user_id>/extend-subscription', methods=['POST'])
@login_required
def extend_user_subscription(user_id):
    """Extend user subscription"""
    data = request.get_json()
    days = data.get('days')
    
    if not days or not isinstance(days, int) or days <= 0:
        return jsonify({'error': 'Valid number of days is required'}), 400
    
    plex_server = current_user.get_plex_server()
    user = User.query.filter_by(id=user_id, plex_server_id=plex_server.id).first()
    
    if not user:
        return jsonify({'error': 'User not found'}), 404
    
    try:
        subscription_service = SubscriptionService(plex_server)
        success, message = subscription_service.extend_subscription(
            user_id, days, current_user.id
        )
        
        if success:
            return jsonify({
                'success': True,
                'message': message,
                'new_end_date': user.subscription_end_date.isoformat(),
                'days_left': user.days_until_expiry(),
                'status': user.get_subscription_status()
            })
        else:
            return jsonify({'success': False, 'message': message}), 400
            
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@api_bp.route('/users/<int:user_id>/cancel-subscription', methods=['POST'])
@login_required
def cancel_user_subscription(user_id):
    """Cancel user subscription"""
    plex_server = current_user.get_plex_server()
    user = User.query.filter_by(id=user_id, plex_server_id=plex_server.id).first()
    
    if not user:
        return jsonify({'error': 'User not found'}), 404
    
    try:
        subscription_service = SubscriptionService(plex_server)
        success, message = subscription_service.cancel_subscription(
            user_id, current_user.id
        )
        
        if success:
            return jsonify({
                'success': True,
                'message': message,
                'status': user.get_subscription_status()
            })
        else:
            return jsonify({'success': False, 'message': message}), 400
            
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@api_bp.route('/users/<int:user_id>/create-subscription', methods=['POST'])
@login_required
def create_user_subscription(user_id):
    """Create new subscription for user"""
    data = request.get_json()
    days = data.get('days')
    plan_name = data.get('plan_name', '')
    
    if not days or not isinstance(days, int) or days <= 0:
        return jsonify({'error': 'Valid number of days is required'}), 400
    
    plex_server = current_user.get_plex_server()
    user = User.query.filter_by(id=user_id, plex_server_id=plex_server.id).first()
    
    if not user:
        return jsonify({'error': 'User not found'}), 404
    
    try:
        start_date = datetime.utcnow()
        end_date = start_date + timedelta(days=days)
        
        subscription_service = SubscriptionService(plex_server)
        success, message = subscription_service.create_subscription(
            user_id, end_date, start_date, plan_name, days, current_user.id
        )
        
        if success:
            return jsonify({
                'success': True,
                'message': message,
                'end_date': user.subscription_end_date.isoformat(),
                'days_left': user.days_until_expiry(),
                'status': user.get_subscription_status()
            })
        else:
            return jsonify({'success': False, 'message': message}), 400
            
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@api_bp.route('/users/<int:user_id>/share-library', methods=['POST'])
@login_required
def share_library_with_user(user_id):
    """Share library with user"""
    data = request.get_json()
    library_id = data.get('library_id')
    
    if not library_id:
        return jsonify({'error': 'Library ID is required'}), 400
    
    plex_server = current_user.get_plex_server()
    user = User.query.filter_by(id=user_id, plex_server_id=plex_server.id).first()
    library = Library.query.filter_by(id=library_id, plex_server_id=plex_server.id).first()
    
    if not user:
        return jsonify({'error': 'User not found'}), 404
    
    if not library:
        return jsonify({'error': 'Library not found'}), 404
    
    try:
        subscription_service = SubscriptionService(plex_server)
        success, message = subscription_service.share_library_with_user(
            user_id, library_id, current_user.id
        )
        
        return jsonify({'success': success, 'message': message})
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@api_bp.route('/users/<int:user_id>/unshare-library', methods=['POST'])
@login_required
def unshare_library_from_user(user_id):
    """Unshare library from user"""
    data = request.get_json()
    library_id = data.get('library_id')
    
    if not library_id:
        return jsonify({'error': 'Library ID is required'}), 400
    
    plex_server = current_user.get_plex_server()
    user = User.query.filter_by(id=user_id, plex_server_id=plex_server.id).first()
    library = Library.query.filter_by(id=library_id, plex_server_id=plex_server.id).first()
    
    if not user:
        return jsonify({'error': 'User not found'}), 404
    
    if not library:
        return jsonify({'error': 'Library not found'}), 404
    
    try:
        subscription_service = SubscriptionService(plex_server)
        success, message = subscription_service.unshare_library_from_user(
            user_id, library_id
        )
        
        return jsonify({'success': success, 'message': message})
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@api_bp.route('/users/<int:user_id>/shared-libraries')
@login_required
def get_user_shared_libraries(user_id):
    """Get user's shared libraries"""
    plex_server = current_user.get_plex_server()
    user = User.query.filter_by(id=user_id, plex_server_id=plex_server.id).first()
    
    if not user:
        return jsonify({'error': 'User not found'}), 404
    
    shared_libraries = SharedLibrary.query.filter_by(user_id=user_id).join(Library).all()
    
    libraries_data = []
    for shared_lib in shared_libraries:
        libraries_data.append({
            'id': shared_lib.library.id,
            'title': shared_lib.library.title,
            'type': shared_lib.library.type,
            'is_shared': shared_lib.is_shared,
            'shared_at': shared_lib.shared_at.isoformat() if shared_lib.shared_at else None,
            'unshared_at': shared_lib.unshared_at.isoformat() if shared_lib.unshared_at else None
        })
    
    return jsonify(libraries_data)

@api_bp.route('/check-expired-subscriptions', methods=['POST'])
@login_required
def check_expired_subscriptions():
    """Check and process expired subscriptions"""
    plex_server = current_user.get_plex_server()
    if not plex_server:
        return jsonify({'error': 'No Plex server configured'}), 400
    
    try:
        subscription_service = SubscriptionService(plex_server)
        success, message = subscription_service.check_expired_subscriptions()
        
        return jsonify({'success': success, 'message': message})
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@api_bp.route('/subscription-countdown/<int:user_id>')
@login_required
def subscription_countdown(user_id):
    """Get real-time subscription countdown data"""
    plex_server = current_user.get_plex_server()
    user = User.query.filter_by(id=user_id, plex_server_id=plex_server.id).first()
    
    if not user:
        return jsonify({'error': 'User not found'}), 404
    
    if not user.subscription_end_date:
        return jsonify({
            'status': 'no_subscription',
            'message': 'No active subscription'
        })
    
    now = datetime.utcnow()
    end_date = user.subscription_end_date
    
    if end_date <= now:
        return jsonify({
            'status': 'expired',
            'message': 'Subscription expired',
            'expired_since': (now - end_date).days
        })
    
    # Calculate time remaining
    time_left = end_date - now
    days_left = time_left.days
    hours_left = int(time_left.seconds / 3600)
    minutes_left = int((time_left.seconds % 3600) / 60)
    
    # Calculate progress percentage
    current_subscription = user.subscriptions.filter_by(status='active').first()
    progress = 0
    if current_subscription:
        progress = current_subscription.progress_percentage()
    
    return jsonify({
        'status': 'active',
        'days_left': days_left,
        'hours_left': hours_left,
        'minutes_left': minutes_left,
        'total_seconds_left': int(time_left.total_seconds()),
        'progress_percentage': progress,
        'end_date': end_date.isoformat(),
        'formatted_time_left': f"{days_left}d {hours_left}h {minutes_left}m"
    })

@api_bp.route('/bulk-extend-subscriptions', methods=['POST'])
@login_required
def bulk_extend_subscriptions():
    """Bulk extend multiple user subscriptions"""
    data = request.get_json()
    user_ids = data.get('user_ids', [])
    days = data.get('days')
    
    if not user_ids or not days or not isinstance(days, int) or days <= 0:
        return jsonify({'error': 'Valid user IDs and days are required'}), 400
    
    plex_server = current_user.get_plex_server()
    subscription_service = SubscriptionService(plex_server)
    
    results = []
    success_count = 0
    
    for user_id in user_ids:
        user = User.query.filter_by(id=user_id, plex_server_id=plex_server.id).first()
        if not user:
            results.append({
                'user_id': user_id,
                'success': False,
                'message': 'User not found'
            })
            continue
        
        try:
            success, message = subscription_service.extend_subscription(
                user_id, days, current_user.id
            )
            
            results.append({
                'user_id': user_id,
                'username': user.username,
                'success': success,
                'message': message
            })
            
            if success:
                success_count += 1
                
        except Exception as e:
            results.append({
                'user_id': user_id,
                'username': user.username if user else 'Unknown',
                'success': False,
                'message': str(e)
            })
    
    return jsonify({
        'success': success_count > 0,
        'message': f'Extended {success_count} out of {len(user_ids)} subscriptions',
        'results': results,
        'success_count': success_count,
        'total_count': len(user_ids)
    })

@api_bp.route('/search-users')
@login_required
def search_users():
    """Search users with filters"""
    plex_server = current_user.get_plex_server()
    if not plex_server:
        return jsonify({'error': 'No Plex server configured'}), 400
    
    search_term = request.args.get('q', '')
    status_filter = request.args.get('status', 'all')
    limit = request.args.get('limit', 20, type=int)
    
    query = User.query.filter_by(plex_server_id=plex_server.id)
    
    # Apply search filter
    if search_term:
        query = query.filter(
            db.or_(
                User.username.contains(search_term),
                User.email.contains(search_term),
                User.title.contains(search_term)
            )
        )
    
    # Apply status filter
    now = datetime.utcnow()
    if status_filter == 'active':
        query = query.filter(User.subscription_end_date > now)
    elif status_filter == 'expired':
        query = query.filter(User.subscription_end_date <= now)
    elif status_filter == 'expiring':
        week_from_now = now + timedelta(days=7)
        query = query.filter(
            User.subscription_end_date > now,
            User.subscription_end_date <= week_from_now
        )
    
    users = query.order_by(User.username).limit(limit).all()
    
    users_data = []
    for user in users:
        users_data.append({
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'title': user.title,
            'subscription_status': user.get_subscription_status(),
            'days_left': user.days_until_expiry(),
            'end_date': user.subscription_end_date.isoformat() if user.subscription_end_date else None
        })
    
    return jsonify(users_data)
