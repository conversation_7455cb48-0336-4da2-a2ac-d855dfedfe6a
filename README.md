# Plex User Subscription Manager

A modern web application for managing Plex user subscriptions and library sharing with real-time countdown timers and automated access control.

## Features

- **User Management**: Import and manage Plex users with subscription tracking
- **Subscription Control**: Create, extend, and cancel user subscriptions with automatic library access management
- **Real-time Countdowns**: Visual countdown timers showing remaining subscription time
- **Library Sharing**: Automatic sharing/unsharing of libraries based on subscription status
- **Admin Management**: Master admin can create additional admin users
- **Plex OAuth Integration**: Secure token retrieval through Plex authentication
- **Modern UI**: Responsive design with Tailwind CSS and Alpine.js for seamless user experience
- **AJAX Updates**: No page refreshes required for most operations

## Technology Stack

- **Backend**: Python Flask with SQLAlchemy
- **Frontend**: Tailwind CSS + Alpine.js for modern, reactive UI
- **Database**: SQLite (configurable to PostgreSQL/MySQL)
- **Authentication**: Flask-Login with role-based access control
- **API Integration**: Plex Media Server API

## Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd plexUserSubscription

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Configuration

Create a `.env` file based on `.env.example`:

```bash
cp .env.example .env
```

Edit `.env` with your configuration:

```env
SECRET_KEY=your-secret-key-here
DATABASE_URL=sqlite:///plex_subscription.db
PLEX_CLIENT_ID=your-plex-client-id
PLEX_CLIENT_SECRET=your-plex-client-secret
PLEX_REDIRECT_URI=http://localhost:5000/auth/plex/callback
FLASK_ENV=development
FLASK_DEBUG=True
```

### 3. Database Setup

Initialize the database:

```bash
python migrations/init_db.py
```

This will:
- Create all database tables
- Prompt you to create a master admin account
- Optionally create sample data for testing

### 4. Run the Application

```bash
python run.py
```

Or use the Flask development server:

```bash
python app.py
```

Access the application at: http://localhost:5000

## Initial Setup

1. **First Login**: Use the master admin credentials you created during database initialization
2. **Plex Server Setup**: Configure your Plex server URL and token in Settings > Plex Server
3. **Library Configuration**: Set default shared libraries in Settings > Library Sharing
4. **Sync Users**: Use the "Sync Plex Data" button to import users from your Plex server

## Usage

### Managing Users

1. **View Users**: Navigate to Users page to see all imported Plex users
2. **Subscription Management**: 
   - Create new subscriptions with custom duration
   - Extend existing subscriptions
   - Cancel subscriptions (automatically unshares libraries)
3. **Library Sharing**: Manually share/unshare specific libraries with users

### Dashboard Features

- **Real-time Stats**: Total users, active subscriptions, expiring soon, expired
- **Countdown Timers**: Live countdown showing time remaining for each subscription
- **Quick Actions**: Sync Plex data, check expired subscriptions
- **Recent Activity**: View recent subscription changes

### Admin Features

- **Master Admin**: Can create additional admin users
- **Plex OAuth**: Secure token retrieval through Plex authentication
- **Settings Management**: Configure Plex server, default libraries, and admin accounts

## API Endpoints

The application provides RESTful API endpoints for AJAX operations:

- `POST /api/users/{id}/extend-subscription` - Extend user subscription
- `POST /api/users/{id}/cancel-subscription` - Cancel user subscription
- `POST /api/users/{id}/share-library` - Share library with user
- `POST /api/users/{id}/unshare-library` - Unshare library from user
- `GET /api/subscription-countdown/{id}` - Get real-time countdown data

## Configuration

### Environment Variables

- `SECRET_KEY`: Flask secret key for session security
- `DATABASE_URL`: Database connection string
- `PLEX_CLIENT_ID`: Plex OAuth client ID
- `PLEX_CLIENT_SECRET`: Plex OAuth client secret
- `PLEX_REDIRECT_URI`: OAuth callback URL

### Plex Server Configuration

1. Get your Plex token from: https://support.plex.tv/articles/*********-finding-an-authentication-token-x-plex-token/
2. Configure server URL (e.g., https://plex.wikizell.com/)
3. Set up default libraries to share with new users

## Development

### Project Structure

```
plexUserSubscription/
├── app/
│   ├── models/          # Database models
│   ├── routes/          # Flask routes/blueprints
│   ├── services/        # Business logic services
│   ├── templates/       # Jinja2 templates
│   └── static/          # CSS, JS, images
├── migrations/          # Database migration scripts
├── config.py           # Application configuration
├── requirements.txt    # Python dependencies
└── run.py             # Application entry point
```

### Adding New Features

1. **Models**: Add new database models in `app/models/`
2. **Services**: Implement business logic in `app/services/`
3. **Routes**: Create API endpoints in `app/routes/`
4. **Templates**: Add UI templates in `app/templates/`
5. **Frontend**: Use Alpine.js for reactive components

### Database Migrations

When adding new fields or tables:

1. Update the model classes
2. Create migration script in `migrations/`
3. Test migration with sample data

## Troubleshooting

### Common Issues

1. **Plex Connection Failed**: 
   - Verify Plex server URL and token
   - Check network connectivity
   - Ensure Plex server is accessible

2. **Database Errors**:
   - Run `python migrations/init_db.py` to recreate tables
   - Check database permissions

3. **OAuth Issues**:
   - Verify Plex client ID and secret
   - Check redirect URI configuration

### Logs

Check the Flask development server output for detailed error messages.

## Security Considerations

- Store Plex tokens encrypted in database
- Use HTTPS in production
- Regularly rotate secret keys
- Implement rate limiting for API endpoints
- Validate all user inputs

## Production Deployment

For production deployment:

1. Set `FLASK_ENV=production`
2. Use a production WSGI server (gunicorn)
3. Configure reverse proxy (nginx)
4. Use PostgreSQL or MySQL database
5. Set up SSL certificates
6. Configure proper logging

## License

This project is licensed under the MIT License.

## Support

For issues and questions, please create an issue in the repository.
