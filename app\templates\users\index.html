{% extends "base.html" %}

{% block title %}Users - Plex User Subscription Manager{% endblock %}
{% block page_title %}Users{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Search and Filters -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <form method="GET" class="flex flex-col sm:flex-row gap-4">
                <div class="flex-1">
                    <label for="search" class="sr-only">Search users</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search h-5 w-5 text-gray-400"></i>
                        </div>
                        <input type="text" name="search" id="search" value="{{ search }}"
                               class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-plex-500 focus:border-plex-500 sm:text-sm"
                               placeholder="Search users...">
                    </div>
                </div>
                
                <div class="sm:w-48">
                    <label for="status" class="sr-only">Filter by status</label>
                    <select name="status" id="status" 
                            class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-plex-500 focus:border-plex-500 sm:text-sm">
                        <option value="all" {% if status_filter == 'all' %}selected{% endif %}>All Users</option>
                        <option value="active" {% if status_filter == 'active' %}selected{% endif %}>Active</option>
                        <option value="expired" {% if status_filter == 'expired' %}selected{% endif %}>Expired</option>
                        <option value="expiring" {% if status_filter == 'expiring' %}selected{% endif %}>Expiring Soon</option>
                    </select>
                </div>
                
                <button type="submit" 
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-plex-600 hover:bg-plex-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-plex-500">
                    <i class="fas fa-filter -ml-1 mr-2 h-4 w-4"></i>
                    Filter
                </button>
            </form>
        </div>
    </div>

    <!-- Users Table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-4 py-5 sm:p-6">
            {% if users %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                User
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Subscription Status
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Time Remaining
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for user in users %}
                        <tr class="hover:bg-gray-50 {% if user.is_local_only %}bg-red-50{% endif %}">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="h-10 w-10 rounded-full {% if user.is_local_only %}bg-red-500{% else %}bg-plex-500{% endif %} flex items-center justify-center">
                                        <span class="text-sm font-medium text-white">{{ user.username[0].upper() }}</span>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium {% if user.is_local_only %}text-red-900{% else %}text-gray-900{% endif %}">
                                            {{ user.username }}
                                            {% if user.is_local_only %}
                                                <span class="ml-2 px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">LOCAL ONLY</span>
                                            {% endif %}
                                        </div>
                                        <div class="text-sm {% if user.is_local_only %}text-red-600{% else %}text-gray-500{% endif %}">{{ user.email or 'No email' }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% set status = user.get_subscription_status() %}
                                {% if status == 'active' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Active
                                </span>
                                {% elif status == 'expired' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    Expired
                                </span>
                                {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                    No Subscription
                                </span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {% if user.subscription_end_date %}
                                <div id="countdown-{{ user.id }}" 
                                     data-countdown="true" 
                                     data-user-id="{{ user.id }}" 
                                     data-end-date="{{ user.subscription_end_date.isoformat() }}">
                                    {% set days_left = user.days_until_expiry() %}
                                    {% if days_left is not none %}
                                        {% if days_left > 0 %}
                                        {{ days_left }} day{{ 's' if days_left != 1 else '' }} left
                                        {% else %}
                                        Expired
                                        {% endif %}
                                    {% endif %}
                                </div>
                                {% else %}
                                <span class="text-gray-400">No subscription</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex items-center space-x-2">
                                    <a href="{{ url_for('main.user_detail', user_id=user.id) }}"
                                       class="text-plex-600 hover:text-plex-900">
                                        View
                                    </a>
                                    {% if user.is_local_only %}
                                        <button onclick="deleteLocalUser({{ user.id }}, '{{ user.username }}')"
                                                class="text-red-600 hover:text-red-900">
                                            Delete
                                        </button>
                                    {% else %}
                                        <button onclick="quickExtend({{ user.id }})"
                                                class="text-green-600 hover:text-green-900">
                                            Extend
                                        </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if pagination.pages > 1 %}
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-6">
                <div class="flex-1 flex justify-between sm:hidden">
                    {% if pagination.has_prev %}
                    <a href="{{ url_for('main.users', page=pagination.prev_num, search=search, status=status_filter) }}" 
                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Previous
                    </a>
                    {% endif %}
                    {% if pagination.has_next %}
                    <a href="{{ url_for('main.users', page=pagination.next_num, search=search, status=status_filter) }}" 
                       class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Next
                    </a>
                    {% endif %}
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            Showing {{ pagination.per_page * (pagination.page - 1) + 1 }} to 
                            {{ pagination.per_page * pagination.page if pagination.page < pagination.pages else pagination.total }} 
                            of {{ pagination.total }} results
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                            {% for page_num in pagination.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != pagination.page %}
                                    <a href="{{ url_for('main.users', page=page_num, search=search, status=status_filter) }}" 
                                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                        {{ page_num }}
                                    </a>
                                    {% else %}
                                    <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-plex-50 text-sm font-medium text-plex-600">
                                        {{ page_num }}
                                    </span>
                                    {% endif %}
                                {% else %}
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                                    ...
                                </span>
                                {% endif %}
                            {% endfor %}
                        </nav>
                    </div>
                </div>
            </div>
            {% endif %}
            {% else %}
            <div class="text-center py-12">
                <i class="fas fa-users h-12 w-12 text-gray-400 mx-auto mb-4"></i>
                <h3 class="text-sm font-medium text-gray-900">No Users Found</h3>
                <p class="text-sm text-gray-500 mt-1">
                    {% if search or status_filter != 'all' %}
                    No users match your current filters.
                    {% else %}
                    No users have been imported from your Plex server yet.
                    {% endif %}
                </p>
                {% if not search and status_filter == 'all' %}
                <div class="mt-4">
                    <button onclick="syncUsers()" 
                            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-plex-600 hover:bg-plex-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-plex-500">
                        <i class="fas fa-sync-alt -ml-1 mr-2 h-4 w-4"></i>
                        Sync Users from Plex
                    </button>
                </div>
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
async function quickExtend(userId) {
    const days = prompt('How many days to extend the subscription?', '30');
    if (days && !isNaN(days) && parseInt(days) > 0) {
        try {
            await PlexApp.subscription.extend(userId, parseInt(days));
            setTimeout(() => location.reload(), 1000);
        } catch (error) {
            // Error already handled by PlexApp.subscription.extend
        }
    }
}

async function deleteLocalUser(userId, username) {
    if (confirm(`Are you sure you want to delete local user "${username}" and all related data? This action cannot be undone.`)) {
        try {
            const response = await fetch(`/api/users/${userId}/delete-local-user`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            const data = await response.json();

            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Delete failed: ' + data.message);
            }
        } catch (error) {
            alert('Delete failed: ' + error.message);
        }
    }
}

async function syncUsers() {
    try {
        const response = await fetch('/api/sync-plex-data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const data = await response.json();

        if (data.success) {
            PlexApp.utils.showNotification(data.message, 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            PlexApp.utils.showNotification('Sync failed: ' + data.message, 'error');
        }
    } catch (error) {
        PlexApp.utils.showNotification('Sync failed: ' + error.message, 'error');
    }
}
</script>
{% endblock %}
