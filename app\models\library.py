from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from app import db

class Library(db.Model):
    """Plex library model"""
    __tablename__ = 'libraries'
    
    id = db.Column(db.Integer, primary_key=True)
    plex_server_id = db.Column(db.Integer, db.<PERSON>ey('plex_servers.id'), nullable=False)
    plex_library_id = db.Column(db.String(100), nullable=False)  # Plex's internal library ID
    title = db.Column(db.String(200), nullable=False)
    type = db.Column(db.String(50), nullable=False)  # movie, show, music, photo, etc.
    agent = db.Column(db.String(100))  # Metadata agent
    scanner = db.Column(db.String(100))  # Library scanner
    language = db.Column(db.String(10))
    uuid = db.Column(db.String(100))
    
    # Library settings
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    is_shared_by_default = db.Column(db.Boolean, default=False, nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_sync = db.Column(db.DateTime)
    
    # Relationships
    shared_libraries = db.relationship('SharedLibrary', backref='library', lazy='dynamic', cascade='all, delete-orphan')
    
    # Unique constraint for plex_library_id per server
    __table_args__ = (db.UniqueConstraint('plex_server_id', 'plex_library_id', name='unique_library_per_server'),)
    
    def __init__(self, plex_server_id, plex_library_id, title, type, **kwargs):
        self.plex_server_id = plex_server_id
        self.plex_library_id = plex_library_id
        self.title = title
        self.type = type
        self.agent = kwargs.get('agent')
        self.scanner = kwargs.get('scanner')
        self.language = kwargs.get('language')
        self.uuid = kwargs.get('uuid')
    
    def get_shared_users_count(self):
        """Get count of users this library is shared with"""
        return self.shared_libraries.filter_by(is_shared=True).count()
    
    def get_shared_users(self):
        """Get list of users this library is shared with"""
        shared = self.shared_libraries.filter_by(is_shared=True).all()
        return [sl.user for sl in shared]
    
    def is_shared_with_user(self, user_id):
        """Check if library is shared with specific user"""
        return self.shared_libraries.filter_by(user_id=user_id, is_shared=True).first() is not None
    
    def update_sync_time(self):
        """Update last sync timestamp"""
        self.last_sync = datetime.utcnow()
        self.updated_at = datetime.utcnow()
    
    def __repr__(self):
        return f'<Library {self.title} ({self.type})>'


class SharedLibrary(db.Model):
    """Junction table for user-library sharing with additional metadata"""
    __tablename__ = 'shared_libraries'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    library_id = db.Column(db.Integer, db.ForeignKey('libraries.id'), nullable=False)
    is_shared = db.Column(db.Boolean, default=True, nullable=False)
    
    # Sharing metadata
    shared_at = db.Column(db.DateTime, default=datetime.utcnow)
    unshared_at = db.Column(db.DateTime)
    shared_by_admin_id = db.Column(db.Integer, db.ForeignKey('admins.id'))
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Unique constraint for user-library combination
    __table_args__ = (db.UniqueConstraint('user_id', 'library_id', name='unique_user_library_share'),)
    
    def __init__(self, user_id, library_id, is_shared=True, shared_by_admin_id=None):
        self.user_id = user_id
        self.library_id = library_id
        self.is_shared = is_shared
        self.shared_by_admin_id = shared_by_admin_id
        if is_shared:
            self.shared_at = datetime.utcnow()
    
    def share(self, admin_id=None):
        """Share the library with user"""
        self.is_shared = True
        self.shared_at = datetime.utcnow()
        self.unshared_at = None
        self.shared_by_admin_id = admin_id
        self.updated_at = datetime.utcnow()
    
    def unshare(self):
        """Unshare the library from user"""
        self.is_shared = False
        self.unshared_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
    
    def __repr__(self):
        status = "shared" if self.is_shared else "unshared"
        return f'<SharedLibrary User:{self.user_id} Library:{self.library_id} ({status})>'
