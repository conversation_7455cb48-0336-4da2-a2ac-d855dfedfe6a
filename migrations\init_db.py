#!/usr/bin/env python3
"""
Database initialization and migration script for Plex User Subscription Management
"""

import os
import sys
from datetime import datetime, timedelta

# Add the parent directory to the path so we can import our app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, db
from app.models import Admin, PlexServer, User, Library, SharedLibrary, Subscription

def init_database():
    """Initialize the database with tables"""
    print("Creating database tables...")
    db.create_all()
    print("Database tables created successfully!")

def create_master_admin():
    """Create the initial master admin user"""
    print("Creating master admin user...")
    
    # Check if master admin already exists
    master_admin = Admin.query.filter_by(is_master=True).first()
    if master_admin:
        print(f"Master admin already exists: {master_admin.username}")
        return master_admin
    
    # Create master admin
    username = input("Enter master admin username (default: admin): ").strip() or "admin"
    email = input("Enter master admin email (default: <EMAIL>): ").strip() or "<EMAIL>"
    password = input("Enter master admin password (default: admin123): ").strip() or "admin123"
    
    master_admin = Admin(
        username=username,
        email=email,
        password=password,
        is_master=True
    )
    
    db.session.add(master_admin)
    db.session.commit()
    
    print(f"Master admin created: {username}")
    return master_admin

def create_sample_data():
    """Create sample data for testing"""
    print("Creating sample data...")
    
    # Get master admin
    master_admin = Admin.query.filter_by(is_master=True).first()
    if not master_admin:
        print("No master admin found. Please create one first.")
        return
    
    # Create sample Plex server
    plex_server = PlexServer.query.filter_by(admin_id=master_admin.id).first()
    if not plex_server:
        plex_server = PlexServer(
            admin_id=master_admin.id,
            name="Main Plex Server",
            url="https://plex.wikizell.com/",
            token="KfQs3Gi6pfzbe4dJTHMU"
        )
        plex_server.machine_identifier = "sample-machine-id"
        plex_server.version = "1.32.0"
        
        db.session.add(plex_server)
        db.session.commit()
        print("Sample Plex server created")
    
    # Create sample libraries
    sample_libraries = [
        {"title": "Movies", "type": "movie", "plex_library_id": "1"},
        {"title": "TV Shows", "type": "show", "plex_library_id": "2"},
        {"title": "Music", "type": "artist", "plex_library_id": "3"},
        {"title": "Photos", "type": "photo", "plex_library_id": "4"}
    ]
    
    for lib_data in sample_libraries:
        existing_lib = Library.query.filter_by(
            plex_server_id=plex_server.id,
            plex_library_id=lib_data["plex_library_id"]
        ).first()
        
        if not existing_lib:
            library = Library(
                plex_server_id=plex_server.id,
                plex_library_id=lib_data["plex_library_id"],
                title=lib_data["title"],
                type=lib_data["type"]
            )
            db.session.add(library)
    
    db.session.commit()
    print("Sample libraries created")
    
    # Create sample users
    sample_users = [
        {"username": "john_doe", "email": "<EMAIL>", "plex_user_id": "user1"},
        {"username": "jane_smith", "email": "<EMAIL>", "plex_user_id": "user2"},
        {"username": "bob_wilson", "email": "<EMAIL>", "plex_user_id": "user3"}
    ]
    
    for user_data in sample_users:
        existing_user = User.query.filter_by(
            plex_server_id=plex_server.id,
            plex_user_id=user_data["plex_user_id"]
        ).first()
        
        if not existing_user:
            user = User(
                plex_server_id=plex_server.id,
                plex_user_id=user_data["plex_user_id"],
                username=user_data["username"],
                email=user_data["email"]
            )
            
            # Set subscription end date (some active, some expired)
            if user_data["username"] == "john_doe":
                user.subscription_end_date = datetime.utcnow() + timedelta(days=30)
            elif user_data["username"] == "jane_smith":
                user.subscription_end_date = datetime.utcnow() + timedelta(days=7)
            else:
                user.subscription_end_date = datetime.utcnow() - timedelta(days=5)  # Expired
            
            db.session.add(user)
    
    db.session.commit()
    print("Sample users created")
    print("Sample data creation completed!")

def main():
    """Main migration function"""
    app = create_app('development')
    
    with app.app_context():
        print("Starting database initialization...")
        
        # Initialize database
        init_database()
        
        # Create master admin
        create_master_admin()
        
        # Ask if user wants sample data
        create_sample = input("Create sample data for testing? (y/N): ").strip().lower()
        if create_sample in ['y', 'yes']:
            create_sample_data()
        
        print("\nDatabase initialization completed!")
        print("You can now run the application with: python app.py")

if __name__ == "__main__":
    main()
