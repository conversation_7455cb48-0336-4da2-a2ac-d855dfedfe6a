{% extends "base.html" %}

{% block title %}Plex Server Setup - Plex User Subscription Manager{% endblock %}
{% block page_title %}Plex Server Setup{% endblock %}

{% block content %}
<div class="max-w-2xl mx-auto">
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                {% if plex_server %}Update{% else %}Configure{% endif %} Plex Server
            </h3>
            
            <form method="POST" x-data="{ loading: false, testing: false }" @submit="loading = true">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                
                <div class="space-y-6">
                    <div>
                        <label for="server_name" class="block text-sm font-medium text-gray-700">
                            Server Name
                        </label>
                        <input type="text" name="server_name" id="server_name" required
                               value="{{ plex_server.name if plex_server else 'Main Plex Server' }}"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-plex-500 focus:border-plex-500 sm:text-sm">
                        <p class="mt-2 text-sm text-gray-500">A friendly name for your Plex server.</p>
                    </div>
                    
                    <div>
                        <label for="server_url" class="block text-sm font-medium text-gray-700">
                            Server URL
                        </label>
                        <input type="url" name="server_url" id="server_url" required
                               value="{{ plex_server.url if plex_server else 'https://plex.wikizell.com/' }}"
                               placeholder="https://your-plex-server.com:32400"
                               class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-plex-500 focus:border-plex-500 sm:text-sm">
                        <p class="mt-2 text-sm text-gray-500">The full URL to your Plex server including port if needed.</p>
                    </div>
                    
                    <div>
                        <label for="token" class="block text-sm font-medium text-gray-700">
                            Plex Token
                        </label>
                        <div class="mt-1 flex rounded-md shadow-sm">
                            <input type="password" name="token" id="token" 
                                   value="{{ session_token or (plex_server.get_token() if plex_server else 'KfQs3Gi6pfzbe4dJTHMU') }}"
                                   placeholder="Your Plex authentication token"
                                   class="flex-1 block w-full border-gray-300 rounded-l-md focus:ring-plex-500 focus:border-plex-500 sm:text-sm">
                            <button type="button" @click="testConnection()" :disabled="testing"
                                    class="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-500 text-sm hover:bg-gray-100 focus:outline-none focus:ring-1 focus:ring-plex-500 focus:border-plex-500 disabled:opacity-50"
                                    :class="{ 'btn-loading': testing }">
                                <span x-show="!testing">Test</span>
                                <span x-show="testing" x-cloak>Testing...</span>
                            </button>
                        </div>
                        <p class="mt-2 text-sm text-gray-500">
                            Your Plex authentication token. 
                            <a href="https://support.plex.tv/articles/204059436-finding-an-authentication-token-x-plex-token/" 
                               target="_blank" class="text-plex-600 hover:text-plex-500">
                                How to find your token
                            </a>
                        </p>
                    </div>
                    
                    <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-info-circle h-5 w-5 text-blue-400"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-blue-800">
                                    Alternative: Use Plex OAuth
                                </h3>
                                <div class="mt-2 text-sm text-blue-700">
                                    <p>You can also authenticate securely through Plex's OAuth system:</p>
                                    <div class="mt-2">
                                        <a href="{{ url_for('auth.plex_auth') }}" 
                                           class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                            <i class="fas fa-sign-in-alt -ml-0.5 mr-2 h-4 w-4"></i>
                                            Authenticate with Plex
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-6 flex items-center justify-end space-x-3">
                    <a href="{{ url_for('admin.settings') }}" 
                       class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-plex-500">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="bg-plex-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-plex-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-plex-500 disabled:opacity-50"
                            :disabled="loading"
                            :class="{ 'btn-loading': loading }">
                        <span x-show="!loading">
                            {% if plex_server %}Update Server{% else %}Save Configuration{% endif %}
                        </span>
                        <span x-show="loading" x-cloak>Saving...</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
async function testConnection() {
    const serverUrl = document.getElementById('server_url').value;
    const token = document.getElementById('token').value;
    
    if (!serverUrl || !token) {
        PlexApp.utils.showNotification('Please enter both server URL and token', 'error');
        return;
    }
    
    try {
        const response = await fetch('/auth/api/test-plex-connection', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                server_url: serverUrl,
                token: token
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            PlexApp.utils.showNotification(
                `Connection successful! Server: ${data.server_info.name} (${data.server_info.version})`, 
                'success'
            );
        } else {
            PlexApp.utils.showNotification('Connection failed: ' + data.message, 'error');
        }
    } catch (error) {
        PlexApp.utils.showNotification('Connection test failed: ' + error.message, 'error');
    }
}
</script>
{% endblock %}
