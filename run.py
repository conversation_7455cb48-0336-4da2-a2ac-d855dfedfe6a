#!/usr/bin/env python3
"""
Run script for Plex User Subscription Manager
"""

import os
import sys
from app import create_app, db

def main():
    """Main function to run the application"""
    # Create the Flask app
    app = create_app()
    
    # Create database tables if they don't exist
    with app.app_context():
        try:
            db.create_all()
            print("Database tables created successfully!")
        except Exception as e:
            print(f"Error creating database tables: {e}")
            sys.exit(1)
    
    # Run the application
    print("Starting Plex User Subscription Manager...")
    print("Access the application at: http://localhost:5000")
    print("Press Ctrl+C to stop the server")
    
    try:
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True
        )
    except KeyboardInterrupt:
        print("\nShutting down gracefully...")
    except Exception as e:
        print(f"Error running application: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
