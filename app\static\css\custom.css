/* Custom CSS for Plex User Subscription Manager */

/* Alpine.js cloak */
[x-cloak] {
    display: none !important;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Progress bar animations */
.progress-bar {
    transition: width 0.3s ease-in-out;
}

/* Countdown timer styles */
.countdown-timer {
    font-variant-numeric: tabular-nums;
}

/* Status badges */
.status-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.status-active {
    @apply bg-green-100 text-green-800;
}

.status-expired {
    @apply bg-red-100 text-red-800;
}

.status-expiring {
    @apply bg-yellow-100 text-yellow-800;
}

.status-cancelled {
    @apply bg-gray-100 text-gray-800;
}

/* Card hover effects */
.card-hover {
    @apply transition-all duration-200 hover:shadow-lg hover:-translate-y-1;
}

/* Button loading state */
.btn-loading {
    @apply relative;
}

.btn-loading::after {
    content: '';
    @apply absolute inset-0 bg-white bg-opacity-20 rounded;
}

.btn-loading::before {
    content: '';
    @apply absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2;
    @apply w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin;
}

/* Table styles */
.table-auto th {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table-auto td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

/* Form styles */
.form-input {
    @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-plex-500 focus:ring-plex-500 sm:text-sm;
}

.form-select {
    @apply block w-full rounded-md border-gray-300 shadow-sm focus:border-plex-500 focus:ring-plex-500 sm:text-sm;
}

.form-checkbox {
    @apply h-4 w-4 text-plex-600 focus:ring-plex-500 border-gray-300 rounded;
}

/* Modal styles */
.modal-overlay {
    @apply fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50;
}

.modal-content {
    @apply relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white;
}

/* Notification styles */
.notification {
    @apply fixed top-4 right-4 z-50 max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden;
}

/* Loading spinner */
.spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-plex-500;
}

/* Responsive utilities */
@media (max-width: 640px) {
    .mobile-hidden {
        display: none;
    }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
    /* Dark mode styles can be added here */
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-break {
        page-break-after: always;
    }
}

/* Animation keyframes */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* Custom focus styles */
.focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-plex-500;
}

/* Tooltip styles */
.tooltip {
    @apply relative;
}

.tooltip::before {
    content: attr(data-tooltip);
    @apply absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2;
    @apply bg-gray-900 text-white text-xs rounded py-1 px-2 whitespace-nowrap;
    @apply opacity-0 pointer-events-none transition-opacity duration-200;
}

.tooltip:hover::before {
    @apply opacity-100;
}

/* Progress ring for countdown */
.progress-ring {
    transform: rotate(-90deg);
    transform-origin: 50% 50%;
}

.progress-ring-circle {
    transition: stroke-dasharray 0.35s;
    transform: rotate(-90deg);
    transform-origin: 50% 50%;
}
