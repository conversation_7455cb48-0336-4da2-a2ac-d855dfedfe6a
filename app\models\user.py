from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from app import db

class User(db.Model):
    """Plex user model imported from Plex server"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    plex_server_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>('plex_servers.id'), nullable=False)
    plex_user_id = db.Column(db.String(100), nullable=False)  # Plex's internal user ID
    username = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120))
    title = db.Column(db.String(100))  # Display name in Plex
    thumb = db.Column(db.String(255))  # Profile picture URL
    is_managed = db.Column(db.<PERSON>, default=False)  # Managed user or not
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    
    # Subscription information
    subscription_end_date = db.Column(db.DateTime)
    subscription_status = db.Column(db.String(20), default='active')  # active, expired, suspended
    
    # Original library sharing setup (stored as JSON)
    original_shared_libraries = db.Column(db.Text)  # JSON string of library IDs
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_sync = db.Column(db.DateTime)
    
    # Relationships
    subscriptions = db.relationship('Subscription', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    shared_libraries = db.relationship('SharedLibrary', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    
    # Unique constraint for plex_user_id per server
    __table_args__ = (db.UniqueConstraint('plex_server_id', 'plex_user_id', name='unique_user_per_server'),)
    
    def __init__(self, plex_server_id, plex_user_id, username, email=None, title=None):
        self.plex_server_id = plex_server_id
        self.plex_user_id = plex_user_id
        self.username = username
        self.email = email
        self.title = title or username
    
    def get_subscription_status(self):
        """Get current subscription status based on end date"""
        if not self.subscription_end_date:
            return 'no_subscription'
        
        now = datetime.utcnow()
        if self.subscription_end_date > now:
            return 'active'
        else:
            return 'expired'
    
    def days_until_expiry(self):
        """Get number of days until subscription expires"""
        if not self.subscription_end_date:
            return None
        
        now = datetime.utcnow()
        if self.subscription_end_date > now:
            delta = self.subscription_end_date - now
            return delta.days
        else:
            return 0
    
    def get_original_libraries(self):
        """Get list of originally shared library IDs"""
        if self.original_shared_libraries:
            import json
            try:
                return json.loads(self.original_shared_libraries)
            except json.JSONDecodeError:
                return []
        return []
    
    def set_original_libraries(self, library_ids):
        """Set originally shared library IDs"""
        import json
        self.original_shared_libraries = json.dumps(library_ids)
        self.updated_at = datetime.utcnow()
    
    def update_subscription(self, end_date, status='active'):
        """Update subscription information"""
        self.subscription_end_date = end_date
        self.subscription_status = status
        self.updated_at = datetime.utcnow()
    
    def get_current_shared_libraries(self):
        """Get currently shared libraries"""
        return [sl.library_id for sl in self.shared_libraries.filter_by(is_shared=True)]

    def get_active_subscription(self):
        """Get the user's active subscription"""
        from app.models.subscription import Subscription
        return Subscription.query.filter_by(
            user_id=self.id,
            status='active'
        ).order_by(Subscription.created_at.desc()).first()

    def __repr__(self):
        return f'<User {self.username} ({self.plex_user_id})>'
