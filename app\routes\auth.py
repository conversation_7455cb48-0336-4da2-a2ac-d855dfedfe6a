"""
Authentication routes for login, logout, and Plex OAuth
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, session, jsonify
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.urls import url_parse
from app import db
from app.models import Admin, PlexServer
from app.services.plex_service import PlexOAuthService, PlexService, PlexAPIError

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """Admin login page"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        remember_me = bool(request.form.get('remember_me'))
        
        if not username or not password:
            flash('Please enter both username and password.', 'error')
            return render_template('auth/login.html')
        
        admin = Admin.query.filter_by(username=username).first()
        
        if admin and admin.check_password(password):
            if not admin.is_active:
                flash('Your account has been deactivated.', 'error')
                return render_template('auth/login.html')
            
            login_user(admin, remember=remember_me)
            admin.update_last_login()
            
            # Check if admin has Plex server configured
            plex_server = admin.get_plex_server()
            if not plex_server:
                flash('Please configure your Plex server settings.', 'info')
                return redirect(url_for('admin.plex_setup'))
            
            # Redirect to next page or dashboard
            next_page = request.args.get('next')
            if not next_page or url_parse(next_page).netloc != '':
                next_page = url_for('main.dashboard')
            
            flash(f'Welcome back, {admin.username}!', 'success')
            return redirect(next_page)
        else:
            flash('Invalid username or password.', 'error')
    
    return render_template('auth/login.html')

@auth_bp.route('/logout')
@login_required
def logout():
    """Admin logout"""
    username = current_user.username
    logout_user()
    flash(f'You have been logged out, {username}.', 'info')
    return redirect(url_for('auth.login'))

@auth_bp.route('/register', methods=['GET', 'POST'])
def register():
    """Admin registration (only for master admin or if no admins exist)"""
    # Check if any admins exist
    admin_count = Admin.query.count()
    
    # If admins exist and user is not logged in as master admin, deny access
    if admin_count > 0:
        if not current_user.is_authenticated or not current_user.is_master:
            flash('Registration is restricted to master administrators.', 'error')
            return redirect(url_for('auth.login'))
    
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        is_master = bool(request.form.get('is_master')) if admin_count == 0 else False
        
        # Validation
        if not all([username, email, password, confirm_password]):
            flash('All fields are required.', 'error')
            return render_template('auth/register.html')
        
        if password != confirm_password:
            flash('Passwords do not match.', 'error')
            return render_template('auth/register.html')
        
        if len(password) < 6:
            flash('Password must be at least 6 characters long.', 'error')
            return render_template('auth/register.html')
        
        # Check if username or email already exists
        if Admin.query.filter_by(username=username).first():
            flash('Username already exists.', 'error')
            return render_template('auth/register.html')
        
        if Admin.query.filter_by(email=email).first():
            flash('Email already exists.', 'error')
            return render_template('auth/register.html')
        
        try:
            # Create new admin
            created_by_id = current_user.id if current_user.is_authenticated else None
            
            new_admin = Admin(
                username=username,
                email=email,
                password=password,
                is_master=is_master,
                created_by_id=created_by_id
            )
            
            db.session.add(new_admin)
            db.session.commit()
            
            flash(f'Admin account created successfully for {username}.', 'success')
            
            # If this is the first admin (master), log them in
            if admin_count == 0:
                login_user(new_admin)
                return redirect(url_for('admin.plex_setup'))
            else:
                return redirect(url_for('admin.manage_admins'))
                
        except Exception as e:
            db.session.rollback()
            flash('An error occurred while creating the account.', 'error')
    
    return render_template('auth/register.html', is_first_admin=(admin_count == 0))

@auth_bp.route('/plex/auth')
@login_required
def plex_auth():
    """Initiate Plex OAuth authentication"""
    try:
        auth_url = PlexOAuthService.get_auth_url()
        return redirect(auth_url)
    except PlexAPIError as e:
        flash(f'Failed to initiate Plex authentication: {str(e)}', 'error')
        return redirect(url_for('admin.plex_setup'))

@auth_bp.route('/plex/callback')
@login_required
def plex_callback():
    """Handle Plex OAuth callback"""
    auth_code = request.args.get('code')
    error = request.args.get('error')
    
    if error:
        flash(f'Plex authentication failed: {error}', 'error')
        return redirect(url_for('admin.plex_setup'))
    
    if not auth_code:
        flash('No authorization code received from Plex.', 'error')
        return redirect(url_for('admin.plex_setup'))
    
    try:
        # Exchange code for token
        token = PlexOAuthService.exchange_code_for_token(auth_code)
        
        # Store token in session temporarily
        session['plex_token'] = token
        flash('Plex authentication successful! Please complete server setup.', 'success')
        
        return redirect(url_for('admin.plex_setup'))
        
    except PlexAPIError as e:
        flash(f'Failed to complete Plex authentication: {str(e)}', 'error')
        return redirect(url_for('admin.plex_setup'))

@auth_bp.route('/api/check-auth')
@login_required
def check_auth():
    """API endpoint to check authentication status"""
    return jsonify({
        'authenticated': True,
        'user': {
            'id': current_user.id,
            'username': current_user.username,
            'email': current_user.email,
            'is_master': current_user.is_master
        }
    })

@auth_bp.route('/api/test-plex-connection', methods=['POST'])
@login_required
def test_plex_connection():
    """API endpoint to test Plex server connection"""
    data = request.get_json()
    server_url = data.get('server_url')
    token = data.get('token')
    
    if not server_url or not token:
        return jsonify({
            'success': False,
            'message': 'Server URL and token are required'
        }), 400
    
    try:
        plex_service = PlexService()
        success, message = plex_service.test_connection(server_url, token)
        
        if success:
            # Get server info
            server_info = plex_service.get_server_info(server_url, token)
            return jsonify({
                'success': True,
                'message': message,
                'server_info': {
                    'name': server_info.get('friendlyName', 'Unknown'),
                    'version': server_info.get('version', 'Unknown'),
                    'machine_identifier': server_info.get('machineIdentifier', '')
                }
            })
        else:
            return jsonify({
                'success': False,
                'message': message
            }), 400
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Connection test failed: {str(e)}'
        }), 500

@auth_bp.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    """Change admin password"""
    if request.method == 'POST':
        current_password = request.form.get('current_password')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')
        
        if not all([current_password, new_password, confirm_password]):
            flash('All fields are required.', 'error')
            return render_template('auth/change_password.html')
        
        if not current_user.check_password(current_password):
            flash('Current password is incorrect.', 'error')
            return render_template('auth/change_password.html')
        
        if new_password != confirm_password:
            flash('New passwords do not match.', 'error')
            return render_template('auth/change_password.html')
        
        if len(new_password) < 6:
            flash('Password must be at least 6 characters long.', 'error')
            return render_template('auth/change_password.html')
        
        try:
            current_user.set_password(new_password)
            db.session.commit()
            flash('Password changed successfully.', 'success')
            return redirect(url_for('main.dashboard'))
        except Exception as e:
            db.session.rollback()
            flash('An error occurred while changing password.', 'error')
    
    return render_template('auth/change_password.html')

# Error handlers
@auth_bp.errorhandler(401)
def unauthorized(error):
    """Handle unauthorized access"""
    if request.is_json:
        return jsonify({'error': 'Unauthorized access'}), 401
    flash('Please log in to access this page.', 'error')
    return redirect(url_for('auth.login'))

@auth_bp.errorhandler(403)
def forbidden(error):
    """Handle forbidden access"""
    if request.is_json:
        return jsonify({'error': 'Access forbidden'}), 403
    flash('You do not have permission to access this page.', 'error')
    return redirect(url_for('main.dashboard'))
