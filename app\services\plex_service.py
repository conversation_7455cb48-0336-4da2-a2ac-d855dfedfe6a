"""
Centralized Plex API service for all Plex-related operations
"""

import requests
import json
import uuid
from datetime import datetime, timedelta
from urllib.parse import urljoin, urlencode
from flask import current_app, url_for
from app import db
from app.models import PlexServer, User, Library, SharedLibrary

class PlexAPIError(Exception):
    """Custom exception for Plex API errors"""
    pass

class PlexService:
    """Centralized service for Plex API operations"""
    
    def __init__(self, plex_server=None):
        self.plex_server = plex_server
        self.base_headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'X-Plex-Client-Identifier': self._get_client_identifier(),
            'X-Plex-Product': 'Plex User Subscription Manager',
            'X-Plex-Version': '1.0.0',
            'X-Plex-Platform': 'Web',
            'X-Plex-Platform-Version': '1.0',
            'X-Plex-Device': 'Web Browser',
            'X-Plex-Device-Name': 'Subscription Manager'
        }
    
    def _get_client_identifier(self):
        """Get or generate client identifier"""
        # In production, this should be stored in config or database
        return str(uuid.uuid4())
    
    def _get_headers(self, token=None):
        """Get headers with authentication token"""
        headers = self.base_headers.copy()
        if token:
            headers['X-Plex-Token'] = token
        elif self.plex_server:
            token = self.plex_server.get_token()
            if token:
                headers['X-Plex-Token'] = token
        return headers
    
    def _make_request(self, method, url, token=None, **kwargs):
        """Make authenticated request to Plex API"""
        headers = self._get_headers(token)
        
        try:
            response = requests.request(method, url, headers=headers, **kwargs)
            response.raise_for_status()
            
            # Handle XML responses (some Plex endpoints return XML)
            content_type = response.headers.get('content-type', '')
            if 'xml' in content_type:
                return self._parse_xml_response(response.text)
            else:
                return response.json() if response.content else {}
                
        except requests.exceptions.RequestException as e:
            raise PlexAPIError(f"Plex API request failed: {str(e)}")
        except json.JSONDecodeError as e:
            raise PlexAPIError(f"Failed to parse Plex API response: {str(e)}")
    
    def _parse_xml_response(self, xml_content):
        """Parse XML response to dictionary (simplified)"""
        # For production, use proper XML parsing library like lxml
        # This is a simplified implementation
        import xml.etree.ElementTree as ET
        try:
            root = ET.fromstring(xml_content)
            return self._xml_to_dict(root)
        except ET.ParseError as e:
            raise PlexAPIError(f"Failed to parse XML response: {str(e)}")
    
    def _xml_to_dict(self, element):
        """Convert XML element to dictionary"""
        result = {}
        
        # Add attributes
        if element.attrib:
            result.update(element.attrib)
        
        # Add text content
        if element.text and element.text.strip():
            if len(element) == 0:
                return element.text.strip()
            result['text'] = element.text.strip()
        
        # Add child elements
        for child in element:
            child_data = self._xml_to_dict(child)
            if child.tag in result:
                if not isinstance(result[child.tag], list):
                    result[child.tag] = [result[child.tag]]
                result[child.tag].append(child_data)
            else:
                result[child.tag] = child_data
        
        return result
    
    def test_connection(self, server_url, token):
        """Test connection to Plex server"""
        try:
            url = urljoin(server_url, '/')
            response = self._make_request('GET', url, token=token)
            return True, "Connection successful"
        except PlexAPIError as e:
            return False, str(e)
    
    def get_server_info(self, server_url=None, token=None):
        """Get Plex server information"""
        url = urljoin(server_url or self.plex_server.url, '/')
        response = self._make_request('GET', url, token=token)
        
        if 'MediaContainer' in response:
            return response['MediaContainer']
        return response
    
    def get_users(self, server_url=None, token=None):
        """Get all users from Plex server using shared_servers API"""
        # Use the plex.tv shared_servers API for accurate user data
        machine_id = self.plex_server.machine_identifier
        if not machine_id:
            raise PlexAPIError("Server machine identifier not available")

        url = f'https://plex.tv/api/servers/{machine_id}/shared_servers'

        try:
            response = self._make_request('GET', url, token=token)
            users = []

            if 'SharedServer' in response:
                shared_servers = response['SharedServer']
                if not isinstance(shared_servers, list):
                    shared_servers = [shared_servers]

                for server in shared_servers:
                    users.append({
                        'id': server.get('userID'),
                        'username': server.get('username'),
                        'email': server.get('email', ''),
                        'title': server.get('username'),
                        'shared_libraries': self._parse_shared_libraries(server)
                    })

            return users
        except PlexAPIError:
            # Fallback to local accounts endpoint
            return self._get_users_fallback(server_url, token)

    def _parse_shared_libraries(self, shared_server):
        """Parse shared libraries from SharedServer XML"""
        shared_libs = {}
        if 'Section' in shared_server:
            sections = shared_server['Section']
            if not isinstance(sections, list):
                sections = [sections]

            for section in sections:
                plex_lib_id = section.get('key')
                is_shared = section.get('shared') == '1'
                shared_libs[plex_lib_id] = is_shared

        return shared_libs

    def _sync_user_shared_libraries(self, user, shared_libraries):
        """Sync user's shared libraries based on Plex data"""
        from app.models import SharedLibrary, Library

        for plex_lib_key, is_shared in shared_libraries.items():
            # Find the library by plex_library_id (key)
            library = Library.query.filter_by(
                plex_server_id=self.plex_server.id,
                plex_library_id=plex_lib_key
            ).first()

            if library:
                # Check if shared library record exists
                shared_lib = SharedLibrary.query.filter_by(
                    user_id=user.id,
                    library_id=library.id
                ).first()

                if shared_lib:
                    # Update existing record
                    shared_lib.is_shared = is_shared
                else:
                    # Create new shared library record
                    shared_lib = SharedLibrary(
                        user_id=user.id,
                        library_id=library.id,
                        is_shared=is_shared
                    )
                    db.session.add(shared_lib)

    def _get_users_fallback(self, server_url=None, token=None):
        """Fallback method to get users"""
        url = urljoin(server_url or self.plex_server.url, '/api/v2/home/<USER>')
        
        try:
            response = self._make_request('GET', url, token=token)
            return response.get('users', [])
        except PlexAPIError:
            return []
    
    def get_libraries(self, server_url=None, token=None):
        """Get all libraries from Plex server"""
        url = urljoin(server_url or self.plex_server.url, '/library/sections')
        response = self._make_request('GET', url, token=token)
        
        if 'MediaContainer' in response and 'Directory' in response['MediaContainer']:
            return response['MediaContainer']['Directory']
        return []
    
    def share_library_with_user(self, user_id, library_id, server_url=None, token=None):
        """Share a library with a specific user"""
        # This is a simplified implementation
        # In practice, you'd need to use Plex's sharing API endpoints
        url = urljoin(server_url or self.plex_server.url, f'/api/v2/shared_servers')
        
        data = {
            'user_id': user_id,
            'library_id': library_id,
            'action': 'share'
        }
        
        try:
            response = self._make_request('POST', url, token=token, json=data)
            return True, "Library shared successfully"
        except PlexAPIError as e:
            return False, str(e)
    
    def unshare_library_from_user(self, user_id, library_id, server_url=None, token=None):
        """Unshare a library from a specific user"""
        url = urljoin(server_url or self.plex_server.url, f'/api/v2/shared_servers')
        
        data = {
            'user_id': user_id,
            'library_id': library_id,
            'action': 'unshare'
        }
        
        try:
            response = self._make_request('DELETE', url, token=token, json=data)
            return True, "Library unshared successfully"
        except PlexAPIError as e:
            return False, str(e)
    
    def sync_users_from_plex(self):
        """Sync users from Plex server to local database using shared_servers API"""
        if not self.plex_server:
            raise PlexAPIError("No Plex server configured")

        try:
            plex_users = self.get_users()
            synced_count = 0
            new_users_count = 0
            removed_users_count = 0

            # Get current Plex user IDs from the shared_servers API
            current_plex_user_ids = [str(plex_user.get('id'))
                                   for plex_user in plex_users
                                   if plex_user.get('id')]

            print(f"Found {len(current_plex_user_ids)} users in Plex shared_servers API: {current_plex_user_ids}")

            # Remove ALL users that are not in Plex (excluding sample users)
            users_to_remove = User.query.filter(
                User.plex_server_id == self.plex_server.id,
                ~User.plex_user_id.in_(current_plex_user_ids),
                ~User.plex_user_id.in_(['user1', 'user2', 'user3'])  # Keep sample users
            ).all()

            for user in users_to_remove:
                print(f"REMOVING user {user.username} (Plex ID: {user.plex_user_id}) - NOT in Plex shared_servers")

                # Delete all related data first
                from app.models import Subscription, SharedLibrary

                # Delete subscriptions
                Subscription.query.filter_by(user_id=user.id).delete()

                # Delete shared libraries
                SharedLibrary.query.filter_by(user_id=user.id).delete()

                # Delete the user
                db.session.delete(user)
                removed_users_count += 1

            # Sync current Plex users with emails and shared libraries
            for plex_user in plex_users:
                user_id = str(plex_user.get('id'))
                username = plex_user.get('username', '')
                email = plex_user.get('email', '')
                shared_libraries = plex_user.get('shared_libraries', {})

                if not user_id or not username:
                    continue

                # Check if user exists
                existing_user = User.query.filter_by(
                    plex_server_id=self.plex_server.id,
                    plex_user_id=user_id
                ).first()

                if existing_user:
                    # Update existing user with email
                    existing_user.username = username
                    existing_user.email = email
                    existing_user.last_sync = datetime.utcnow()

                    # Update shared libraries
                    self._sync_user_shared_libraries(existing_user, shared_libraries)
                    print(f"Updated user {username} with email: {email}")
                else:
                    # Create new user with 30-day subscription
                    new_user = User(
                        plex_server_id=self.plex_server.id,
                        plex_user_id=user_id,
                        username=username,
                        email=email
                    )
                    db.session.add(new_user)
                    db.session.flush()  # Get the user ID

                    # Create 30-day subscription for new user
                    from app.models import Subscription
                    subscription = Subscription(
                        user_id=new_user.id,
                        end_date=datetime.utcnow() + timedelta(days=30),
                        plan_name="Default 30-day Plan"
                    )
                    subscription.status = 'active'
                    subscription.plan_duration_days = 30
                    db.session.add(subscription)

                    # Set up shared libraries
                    self._sync_user_shared_libraries(new_user, shared_libraries)

                    synced_count += 1
                    new_users_count += 1
                    print(f"Created new user {username} with email: {email} and 30-day subscription")

            db.session.commit()
            self.plex_server.update_sync_time()

            message = f"Synced {synced_count} users from Plex"
            if new_users_count > 0:
                message += f" ({new_users_count} new users with 30-day subscriptions)"
            if removed_users_count > 0:
                message += f" (removed {removed_users_count} users no longer in Plex)"

            return True, message

        except Exception as e:
            db.session.rollback()
            raise PlexAPIError(f"Failed to sync users: {str(e)}")
    
    def sync_libraries_from_plex(self):
        """Sync libraries from Plex server to local database"""
        if not self.plex_server:
            raise PlexAPIError("No Plex server configured")
        
        try:
            plex_libraries = self.get_libraries()
            synced_count = 0
            
            for plex_lib in plex_libraries:
                lib_id = str(plex_lib.get('key', ''))
                title = plex_lib.get('title', '')
                lib_type = plex_lib.get('type', '')
                
                if not lib_id or not title:
                    continue
                
                # Check if library exists
                existing_lib = Library.query.filter_by(
                    plex_server_id=self.plex_server.id,
                    plex_library_id=lib_id
                ).first()
                
                if existing_lib:
                    # Update existing library
                    existing_lib.title = title
                    existing_lib.type = lib_type
                    existing_lib.last_sync = datetime.utcnow()
                else:
                    # Create new library
                    new_lib = Library(
                        plex_server_id=self.plex_server.id,
                        plex_library_id=lib_id,
                        title=title,
                        type=lib_type,
                        agent=plex_lib.get('agent'),
                        scanner=plex_lib.get('scanner'),
                        language=plex_lib.get('language'),
                        uuid=plex_lib.get('uuid')
                    )
                    db.session.add(new_lib)
                    synced_count += 1
            
            db.session.commit()
            self.plex_server.update_sync_time()
            
            return True, f"Synced {synced_count} libraries from Plex"
            
        except Exception as e:
            db.session.rollback()
            raise PlexAPIError(f"Failed to sync libraries: {str(e)}")

# OAuth related functions
class PlexOAuthService:
    """Service for handling Plex OAuth authentication"""
    
    @staticmethod
    def get_auth_url(redirect_uri=None):
        """Generate Plex OAuth authorization URL"""
        client_id = current_app.config.get('PLEX_CLIENT_ID')
        if not client_id:
            raise PlexAPIError("Plex Client ID not configured")
        
        redirect_uri = redirect_uri or current_app.config.get('PLEX_REDIRECT_URI')
        
        params = {
            'clientID': client_id,
            'context[device][product]': 'Plex User Subscription Manager',
            'context[device][version]': '1.0.0',
            'context[device][platform]': 'Web',
            'context[device][platformVersion]': '1.0',
            'context[device][device]': 'Web Browser',
            'context[device][deviceName]': 'Subscription Manager',
            'context[device][model]': 'Web',
            'context[device][vendor]': 'Plex',
        }
        
        if redirect_uri:
            params['forwardUrl'] = redirect_uri
        
        auth_url = f"{current_app.config.get('PLEX_AUTH_URL')}?{urlencode(params)}"
        return auth_url
    
    @staticmethod
    def exchange_code_for_token(auth_code):
        """Exchange authorization code for access token"""
        # This is a simplified implementation
        # In practice, you'd make a request to Plex's token endpoint
        # For now, we'll return the auth_code as token (for demo purposes)
        return auth_code
