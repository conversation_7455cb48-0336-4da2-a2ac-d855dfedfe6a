"""
Centralized Plex API service for all Plex-related operations
"""

import requests
import json
import uuid
from datetime import datetime, timedelta
from urllib.parse import urljoin, urlencode
from flask import current_app, url_for
from app import db
from app.models import PlexServer, User, Library, SharedLibrary

class PlexAPIError(Exception):
    """Custom exception for Plex API errors"""
    pass

class PlexService:
    """Centralized service for Plex API operations"""
    
    def __init__(self, plex_server=None):
        self.plex_server = plex_server
        self.base_headers = {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'X-Plex-Client-Identifier': self._get_client_identifier(),
            'X-Plex-Product': 'Plex User Subscription Manager',
            'X-Plex-Version': '1.0.0',
            'X-Plex-Platform': 'Web',
            'X-Plex-Platform-Version': '1.0',
            'X-Plex-Device': 'Web Browser',
            'X-Plex-Device-Name': 'Subscription Manager'
        }
    
    def _get_client_identifier(self):
        """Get or generate client identifier"""
        # In production, this should be stored in config or database
        return str(uuid.uuid4())
    
    def _get_headers(self, token=None):
        """Get headers with authentication token"""
        headers = self.base_headers.copy()
        if token:
            headers['X-Plex-Token'] = token
        elif self.plex_server:
            token = self.plex_server.get_token()
            if token:
                headers['X-Plex-Token'] = token
        return headers
    
    def _make_request(self, method, url, token=None, **kwargs):
        """Make authenticated request to Plex API"""
        headers = self._get_headers(token)
        
        try:
            response = requests.request(method, url, headers=headers, **kwargs)
            response.raise_for_status()

            # Handle empty responses (common for DELETE requests)
            if not response.content:
                return {}

            # Handle XML responses (some Plex endpoints return XML)
            content_type = response.headers.get('content-type', '')
            if 'xml' in content_type:
                return self._parse_xml_response(response.text)
            else:
                return response.json() if response.content else {}

        except requests.exceptions.RequestException as e:
            raise PlexAPIError(f"Plex API request failed: {str(e)}")
        except json.JSONDecodeError as e:
            raise PlexAPIError(f"Failed to parse Plex API response: {str(e)}")
    
    def _parse_xml_response(self, xml_content):
        """Parse XML response to dictionary (simplified)"""
        # For production, use proper XML parsing library like lxml
        # This is a simplified implementation
        import xml.etree.ElementTree as ET
        try:
            root = ET.fromstring(xml_content)
            return self._xml_to_dict(root)
        except ET.ParseError as e:
            raise PlexAPIError(f"Failed to parse XML response: {str(e)}")
    
    def _xml_to_dict(self, element):
        """Convert XML element to dictionary"""
        result = {}
        
        # Add attributes
        if element.attrib:
            result.update(element.attrib)
        
        # Add text content
        if element.text and element.text.strip():
            if len(element) == 0:
                return element.text.strip()
            result['text'] = element.text.strip()
        
        # Add child elements
        for child in element:
            child_data = self._xml_to_dict(child)
            if child.tag in result:
                if not isinstance(result[child.tag], list):
                    result[child.tag] = [result[child.tag]]
                result[child.tag].append(child_data)
            else:
                result[child.tag] = child_data
        
        return result
    
    def test_connection(self, server_url, token):
        """Test connection to Plex server"""
        try:
            url = urljoin(server_url, '/')
            response = self._make_request('GET', url, token=token)
            return True, "Connection successful"
        except PlexAPIError as e:
            return False, str(e)
    
    def get_server_info(self, server_url=None, token=None):
        """Get Plex server information"""
        url = urljoin(server_url or self.plex_server.url, '/')
        response = self._make_request('GET', url, token=token)
        
        if 'MediaContainer' in response:
            return response['MediaContainer']
        return response
    
    def get_users(self, server_url=None, token=None):
        """Get all users from Plex server using shared_servers API"""
        # Use the plex.tv shared_servers API for accurate user data
        machine_id = self.plex_server.machine_identifier
        if not machine_id:
            raise PlexAPIError("Server machine identifier not available")

        url = f'https://plex.tv/api/servers/{machine_id}/shared_servers'

        try:
            response = self._make_request('GET', url, token=token)
            users = []

            if 'SharedServer' in response:
                shared_servers = response['SharedServer']
                if not isinstance(shared_servers, list):
                    shared_servers = [shared_servers]

                for server in shared_servers:
                    users.append({
                        'id': server.get('userID'),
                        'username': server.get('username'),
                        'email': server.get('email', ''),
                        'title': server.get('username'),
                        'shared_libraries': self._parse_shared_libraries(server)
                    })

            return users
        except PlexAPIError:
            # Fallback to local accounts endpoint
            return self._get_users_fallback(server_url, token)

    def _parse_shared_libraries(self, shared_server):
        """Parse shared libraries from SharedServer XML"""
        shared_libs = {}
        if 'Section' in shared_server:
            sections = shared_server['Section']
            if not isinstance(sections, list):
                sections = [sections]

            for section in sections:
                plex_lib_id = section.get('key')
                is_shared = section.get('shared') == '1'
                shared_libs[plex_lib_id] = is_shared

        return shared_libs

    def _sync_user_shared_libraries(self, user, shared_libraries):
        """Sync user's shared libraries based on Plex data"""
        from app.models import SharedLibrary, Library

        for plex_lib_key, is_shared in shared_libraries.items():
            # Find the library by plex_library_id (key)
            library = Library.query.filter_by(
                plex_server_id=self.plex_server.id,
                plex_library_id=plex_lib_key
            ).first()

            if library:
                # Check if shared library record exists
                shared_lib = SharedLibrary.query.filter_by(
                    user_id=user.id,
                    library_id=library.id
                ).first()

                if shared_lib:
                    # Update existing record
                    shared_lib.is_shared = is_shared
                else:
                    # Create new shared library record
                    shared_lib = SharedLibrary(
                        user_id=user.id,
                        library_id=library.id,
                        is_shared=is_shared
                    )
                    db.session.add(shared_lib)

    def _get_users_fallback(self, server_url=None, token=None):
        """Fallback method to get users"""
        url = urljoin(server_url or self.plex_server.url, '/api/v2/home/<USER>')
        
        try:
            response = self._make_request('GET', url, token=token)
            return response.get('users', [])
        except PlexAPIError:
            return []
    
    def get_libraries(self, server_url=None, token=None):
        """Get all libraries from Plex server"""
        url = urljoin(server_url or self.plex_server.url, '/library/sections')
        response = self._make_request('GET', url, token=token)
        
        if 'MediaContainer' in response and 'Directory' in response['MediaContainer']:
            return response['MediaContainer']['Directory']
        return []
    
    def share_library_with_user(self, user_plex_id, library_plex_id, token=None):
        """Share a library with a user - Manual process required for existing users"""
        # The Plex API POST endpoint for shared_servers is for creating NEW shares
        # For existing users, we need to use the Plex web interface
        # This function provides verification and guidance

        # Check if user exists in shared servers
        shared_server_id = self._get_shared_server_id(user_plex_id, token)
        if not shared_server_id:
            return False, "User not found in shared servers. Please invite them first through Plex interface."

        # Check if library is already shared
        current_libraries = self._get_user_shared_libraries(user_plex_id, token)
        if library_plex_id in current_libraries:
            return False, f"Library {library_plex_id} is already shared with user {user_plex_id}"

        # For existing users, the API doesn't support adding individual libraries
        # The user must use the Plex web interface
        return False, "Automatic sharing not supported for existing users. Please use Plex web interface to share libraries."
    
    def unshare_library_from_user(self, user_plex_id, library_plex_id, token=None):
        """Unshare a library from a user - Supports complete unshare via API"""
        machine_id = self.plex_server.machine_identifier

        # First get the shared_server_id for this user
        shared_server_id = self._get_shared_server_id(user_plex_id, token)
        if not shared_server_id:
            return False, "User is not currently sharing any libraries"

        # Get current shared libraries for this user
        current_libraries = self._get_user_shared_libraries(user_plex_id, token)

        # Check if the library is actually shared (convert to string for comparison)
        library_plex_id_str = str(library_plex_id)
        if library_plex_id_str not in current_libraries:
            return False, f"Library {library_plex_id} is not currently shared with user {user_plex_id}"

        # Check if this is the only library shared with the user
        if len(current_libraries) == 1:
            # If this is the only library, we can remove the user completely using the working DELETE API
            url = f'https://plex.tv/api/servers/{machine_id}/shared_servers/{shared_server_id}'

            try:
                self._make_request('DELETE', url, token=token)
                return True, f"Library {library_plex_id} unshared successfully. User completely removed from Plex sharing (was their only shared library)."
            except PlexAPIError as e:
                return False, f"Failed to unshare via Plex: {str(e)}"
        else:
            # User has multiple libraries - partial unsharing requires manual intervention
            return False, f"User has {len(current_libraries)} shared libraries. Automatic partial unsharing not supported. Please manually unshare the library through Plex web interface."

    def _get_user_shared_libraries(self, user_plex_id, token=None):
        """Get list of library IDs currently shared with a user"""
        machine_id = self.plex_server.machine_identifier
        url = f'https://plex.tv/api/servers/{machine_id}/shared_servers'

        try:
            response = self._make_request('GET', url, token=token)
            if isinstance(response, dict) and 'SharedServer' in response:
                shared_servers = response['SharedServer']
                if not isinstance(shared_servers, list):
                    shared_servers = [shared_servers]

                for server in shared_servers:
                    if server.get('userID') == str(user_plex_id):
                        libraries = []
                        if 'Section' in server:
                            sections = server['Section']
                            if not isinstance(sections, list):
                                sections = [sections]

                            for section in sections:
                                if section.get('shared') == '1':
                                    libraries.append(section.get('key'))
                        return libraries
            return []
        except PlexAPIError:
            return []

    def _get_shared_server_id(self, user_plex_id, token=None):
        """Get the shared_server_id for a user"""
        machine_id = self.plex_server.machine_identifier
        url = f'https://plex.tv/api/servers/{machine_id}/shared_servers'

        try:
            response = self._make_request('GET', url, token=token)
            if isinstance(response, dict) and 'SharedServer' in response:
                shared_servers = response['SharedServer']
                if not isinstance(shared_servers, list):
                    shared_servers = [shared_servers]

                for server in shared_servers:
                    if server.get('userID') == str(user_plex_id):
                        return server.get('id')
            return None
        except PlexAPIError:
            return None

    def sync_users_from_plex(self):
        """Sync users from Plex server to local database using shared_servers API"""
        if not self.plex_server:
            raise PlexAPIError("No Plex server configured")

        try:
            plex_users = self.get_users()
            synced_count = 0
            new_users_count = 0
            removed_users_count = 0

            # Get current Plex user IDs from the shared_servers API
            current_plex_user_ids = [str(plex_user.get('id'))
                                   for plex_user in plex_users
                                   if plex_user.get('id')]

            print(f"Found {len(current_plex_user_ids)} users in Plex shared_servers API: {current_plex_user_ids}")

            # Remove test users completely (they should never exist)
            test_users = User.query.filter(
                User.plex_server_id == self.plex_server.id,
                User.plex_user_id.in_(['user1', 'user2', 'user3'])
            ).all()

            for user in test_users:
                print(f"REMOVING TEST USER {user.username} (Plex ID: {user.plex_user_id}) - Test users not allowed")

                # Delete all related data first
                from app.models import Subscription, SharedLibrary

                # Delete subscriptions
                Subscription.query.filter_by(user_id=user.id).delete()

                # Delete shared libraries
                SharedLibrary.query.filter_by(user_id=user.id).delete()

                # Delete the user
                db.session.delete(user)
                removed_users_count += 1

            # Mark users that are no longer in Plex as local-only (don't delete them)
            users_not_in_plex = User.query.filter(
                User.plex_server_id == self.plex_server.id,
                ~User.plex_user_id.in_(current_plex_user_ids),
                User.is_local_only == False  # Only mark users that aren't already marked
            ).all()

            for user in users_not_in_plex:
                print(f"MARKING user {user.username} (Plex ID: {user.plex_user_id}) as LOCAL-ONLY - removed from Plex")
                user.is_local_only = True
                user.is_active = False  # Deactivate them
                removed_users_count += 1

            # Sync current Plex users with emails and shared libraries
            for plex_user in plex_users:
                user_id = str(plex_user.get('id'))
                username = plex_user.get('username', '')
                email = plex_user.get('email', '')
                shared_libraries = plex_user.get('shared_libraries', {})

                if not user_id or not username:
                    continue

                # Check if user exists
                existing_user = User.query.filter_by(
                    plex_server_id=self.plex_server.id,
                    plex_user_id=user_id
                ).first()

                if existing_user:
                    # Update existing user with email
                    existing_user.username = username
                    existing_user.email = email
                    existing_user.last_sync = datetime.utcnow()

                    # If user was marked as local-only, reactivate them
                    if existing_user.is_local_only:
                        existing_user.is_local_only = False
                        existing_user.is_active = True
                        print(f"REACTIVATED user {username} - back in Plex")

                    # Update shared libraries
                    self._sync_user_shared_libraries(existing_user, shared_libraries)
                    print(f"Updated user {username} with email: {email}")
                else:
                    # Create new user with 30-day subscription
                    new_user = User(
                        plex_server_id=self.plex_server.id,
                        plex_user_id=user_id,
                        username=username,
                        email=email
                    )
                    db.session.add(new_user)
                    db.session.flush()  # Get the user ID

                    # Create 30-day subscription for new user
                    from app.models import Subscription
                    subscription = Subscription(
                        user_id=new_user.id,
                        end_date=datetime.utcnow() + timedelta(days=30),
                        plan_name="Default 30-day Plan"
                    )
                    subscription.status = 'active'
                    subscription.plan_duration_days = 30
                    db.session.add(subscription)

                    # Set up shared libraries
                    self._sync_user_shared_libraries(new_user, shared_libraries)

                    synced_count += 1
                    new_users_count += 1
                    print(f"Created new user {username} with email: {email} and 30-day subscription")

            db.session.commit()
            self.plex_server.update_sync_time()

            message = f"Synced {synced_count} users from Plex"
            if new_users_count > 0:
                message += f" ({new_users_count} new users with 30-day subscriptions)"
            if removed_users_count > 0:
                message += f" (removed {removed_users_count} users no longer in Plex)"

            return True, message

        except Exception as e:
            db.session.rollback()
            raise PlexAPIError(f"Failed to sync users: {str(e)}")
    
    def sync_libraries_from_plex(self):
        """Sync libraries from Plex server to local database"""
        if not self.plex_server:
            raise PlexAPIError("No Plex server configured")
        
        try:
            plex_libraries = self.get_libraries()
            synced_count = 0
            
            for plex_lib in plex_libraries:
                lib_id = str(plex_lib.get('key', ''))
                title = plex_lib.get('title', '')
                lib_type = plex_lib.get('type', '')
                
                if not lib_id or not title:
                    continue
                
                # Check if library exists
                existing_lib = Library.query.filter_by(
                    plex_server_id=self.plex_server.id,
                    plex_library_id=lib_id
                ).first()
                
                if existing_lib:
                    # Update existing library
                    existing_lib.title = title
                    existing_lib.type = lib_type
                    existing_lib.last_sync = datetime.utcnow()
                else:
                    # Create new library
                    new_lib = Library(
                        plex_server_id=self.plex_server.id,
                        plex_library_id=lib_id,
                        title=title,
                        type=lib_type,
                        agent=plex_lib.get('agent'),
                        scanner=plex_lib.get('scanner'),
                        language=plex_lib.get('language'),
                        uuid=plex_lib.get('uuid')
                    )
                    db.session.add(new_lib)
                    synced_count += 1
            
            db.session.commit()
            self.plex_server.update_sync_time()
            
            return True, f"Synced {synced_count} libraries from Plex"
            
        except Exception as e:
            db.session.rollback()
            raise PlexAPIError(f"Failed to sync libraries: {str(e)}")

# OAuth related functions
class PlexOAuthService:
    """Service for handling Plex OAuth authentication"""
    
    @staticmethod
    def get_auth_url(redirect_uri=None):
        """Generate Plex OAuth authorization URL"""
        client_id = current_app.config.get('PLEX_CLIENT_ID')
        if not client_id:
            raise PlexAPIError("Plex Client ID not configured")
        
        redirect_uri = redirect_uri or current_app.config.get('PLEX_REDIRECT_URI')
        
        params = {
            'clientID': client_id,
            'context[device][product]': 'Plex User Subscription Manager',
            'context[device][version]': '1.0.0',
            'context[device][platform]': 'Web',
            'context[device][platformVersion]': '1.0',
            'context[device][device]': 'Web Browser',
            'context[device][deviceName]': 'Subscription Manager',
            'context[device][model]': 'Web',
            'context[device][vendor]': 'Plex',
        }
        
        if redirect_uri:
            params['forwardUrl'] = redirect_uri
        
        auth_url = f"{current_app.config.get('PLEX_AUTH_URL')}?{urlencode(params)}"
        return auth_url
    
    @staticmethod
    def exchange_code_for_token(auth_code):
        """Exchange authorization code for access token"""
        # This is a simplified implementation
        # In practice, you'd make a request to Plex's token endpoint
        # For now, we'll return the auth_code as token (for demo purposes)
        return auth_code
