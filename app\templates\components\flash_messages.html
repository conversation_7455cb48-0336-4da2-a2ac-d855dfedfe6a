<!-- Flash messages component -->
{% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
        <div x-data="{ show: true }" x-show="show" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform scale-90" x-transition:enter-end="opacity-100 transform scale-100" x-transition:leave="transition ease-in duration-300" x-transition:leave-start="opacity-100 transform scale-100" x-transition:leave-end="opacity-0 transform scale-90" class="mb-6">
            {% for category, message in messages %}
                <div class="rounded-md p-4 mb-4 {% if category == 'error' %}bg-red-50 border border-red-200{% elif category == 'warning' %}bg-yellow-50 border border-yellow-200{% elif category == 'info' %}bg-blue-50 border border-blue-200{% else %}bg-green-50 border border-green-200{% endif %}">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            {% if category == 'error' %}
                                <i class="fas fa-exclamation-circle h-5 w-5 text-red-400"></i>
                            {% elif category == 'warning' %}
                                <i class="fas fa-exclamation-triangle h-5 w-5 text-yellow-400"></i>
                            {% elif category == 'info' %}
                                <i class="fas fa-info-circle h-5 w-5 text-blue-400"></i>
                            {% else %}
                                <i class="fas fa-check-circle h-5 w-5 text-green-400"></i>
                            {% endif %}
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium {% if category == 'error' %}text-red-800{% elif category == 'warning' %}text-yellow-800{% elif category == 'info' %}text-blue-800{% else %}text-green-800{% endif %}">
                                {{ message }}
                            </p>
                        </div>
                        <div class="ml-auto pl-3">
                            <div class="-mx-1.5 -my-1.5">
                                <button type="button" @click="show = false" class="inline-flex rounded-md p-1.5 {% if category == 'error' %}text-red-500 hover:bg-red-100{% elif category == 'warning' %}text-yellow-500 hover:bg-yellow-100{% elif category == 'info' %}text-blue-500 hover:bg-blue-100{% else %}text-green-500 hover:bg-green-100{% endif %} focus:outline-none focus:ring-2 focus:ring-offset-2 {% if category == 'error' %}focus:ring-red-600{% elif category == 'warning' %}focus:ring-yellow-600{% elif category == 'info' %}focus:ring-blue-600{% else %}focus:ring-green-600{% endif %}">
                                    <span class="sr-only">Dismiss</span>
                                    <i class="fas fa-times h-5 w-5"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% endif %}
{% endwith %}
