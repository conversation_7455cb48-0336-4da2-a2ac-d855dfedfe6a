from datetime import datetime
from flask_sqlalchemy import SQLAlchemy
from cryptography.fernet import Fernet
import os
import base64
from app import db

class PlexServer(db.Model):
    """Plex server configuration for each admin"""
    __tablename__ = 'plex_servers'
    
    id = db.Column(db.Integer, primary_key=True)
    admin_id = db.Column(db.Integer, db.ForeignKey('admins.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    url = db.Column(db.String(255), nullable=False)
    token_encrypted = db.Column(db.Text, nullable=False)
    machine_identifier = db.Column(db.String(100), unique=True)
    version = db.Column(db.String(50))
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    last_sync = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Default library sharing settings
    default_shared_libraries = db.Column(db.Text)  # JSON string of library IDs
    
    # Relationships
    users = db.relationship('User', backref='plex_server', lazy='dynamic', cascade='all, delete-orphan')
    libraries = db.relationship('Library', backref='plex_server', lazy='dynamic', cascade='all, delete-orphan')
    
    def __init__(self, admin_id, name, url, token):
        self.admin_id = admin_id
        self.name = name
        self.url = url
        self.set_token(token)
    
    @staticmethod
    def _get_encryption_key():
        """Get or create encryption key for token storage"""
        key = os.environ.get('ENCRYPTION_KEY')
        if not key:
            # Generate a new key if not exists (for development)
            key = Fernet.generate_key()
            # In production, this should be stored securely
        else:
            key = key.encode()
        return key
    
    def set_token(self, token):
        """Encrypt and store Plex token"""
        if token:
            # For now, store token directly (TODO: implement proper encryption)
            self.token_encrypted = base64.b64encode(token.encode()).decode()
    
    def get_token(self):
        """Decrypt and return Plex token"""
        if self.token_encrypted:
            try:
                # For now, decode token directly (TODO: implement proper decryption)
                return base64.b64decode(self.token_encrypted.encode()).decode()
            except Exception as e:
                print(f"Token decryption error: {e}")
                return None
        return None
    
    def update_sync_time(self):
        """Update last sync timestamp"""
        self.last_sync = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        db.session.commit()
    
    def get_default_libraries(self):
        """Get list of default shared library IDs"""
        if self.default_shared_libraries:
            import json
            try:
                return json.loads(self.default_shared_libraries)
            except json.JSONDecodeError:
                return []
        return []
    
    def set_default_libraries(self, library_ids):
        """Set default shared library IDs"""
        import json
        self.default_shared_libraries = json.dumps(library_ids)
        self.updated_at = datetime.utcnow()
    
    def __repr__(self):
        return f'<PlexServer {self.name} ({self.url})>'
