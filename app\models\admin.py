from datetime import datetime
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from app import db

class Admin(UserMixin, db.Model):
    """Admin user model for managing Plex servers and users"""
    __tablename__ = 'admins'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False, index=True)
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(255), nullable=False)
    is_master = db.Column(db.Boolean, default=False, nullable=False)
    is_active = db.Column(db.Bo<PERSON>an, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    
    # Relationships
    plex_servers = db.relationship('PlexServer', backref='admin', lazy='dynamic', cascade='all, delete-orphan')
    created_admins = db.relationship('Admin', backref='creator', remote_side=[id])
    created_by_id = db.Column(db.Integer, db.ForeignKey('admins.id'))
    
    def __init__(self, username, email, password, is_master=False, created_by_id=None):
        self.username = username
        self.email = email
        self.set_password(password)
        self.is_master = is_master
        self.created_by_id = created_by_id
    
    def set_password(self, password):
        """Hash and set password"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """Check if provided password matches hash"""
        return check_password_hash(self.password_hash, password)
    
    def update_last_login(self):
        """Update last login timestamp"""
        self.last_login = datetime.utcnow()
        db.session.commit()
    
    def can_create_admins(self):
        """Check if admin can create other admins (only master admins)"""
        return self.is_master
    
    def get_plex_server(self):
        """Get the primary Plex server for this admin"""
        return self.plex_servers.first()
    
    def __repr__(self):
        return f'<Admin {self.username}>'
