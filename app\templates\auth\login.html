{% extends "base.html" %}

{% block title %}Login - Plex User Subscription Manager{% endblock %}

{% block auth_content %}
<div class="max-w-md w-full space-y-8">
    <div>
        <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-plex-500">
            <i class="fas fa-play text-white text-xl"></i>
        </div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Sign in to your account
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
            Plex User Subscription Manager
        </p>
    </div>
    
    <form class="mt-8 space-y-6" method="POST" x-data="{ loading: false }" @submit="loading = true">
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
        
        <div class="rounded-md shadow-sm -space-y-px">
            <div>
                <label for="username" class="sr-only">Username</label>
                <input id="username" name="username" type="text" required 
                       class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-plex-500 focus:border-plex-500 focus:z-10 sm:text-sm" 
                       placeholder="Username">
            </div>
            <div>
                <label for="password" class="sr-only">Password</label>
                <input id="password" name="password" type="password" required 
                       class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-plex-500 focus:border-plex-500 focus:z-10 sm:text-sm" 
                       placeholder="Password">
            </div>
        </div>

        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <input id="remember_me" name="remember_me" type="checkbox" 
                       class="h-4 w-4 text-plex-600 focus:ring-plex-500 border-gray-300 rounded">
                <label for="remember_me" class="ml-2 block text-sm text-gray-900">
                    Remember me
                </label>
            </div>
        </div>

        <div>
            <button type="submit" 
                    class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-plex-600 hover:bg-plex-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-plex-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    :disabled="loading"
                    :class="{ 'btn-loading': loading }">
                <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                    <i class="fas fa-lock h-5 w-5 text-plex-500 group-hover:text-plex-400"></i>
                </span>
                <span x-show="!loading">Sign in</span>
                <span x-show="loading" x-cloak>Signing in...</span>
            </button>
        </div>

        <div class="text-center">
            <p class="text-sm text-gray-600">
                Need to create the first admin account?
                <a href="{{ url_for('auth.register') }}" class="font-medium text-plex-600 hover:text-plex-500">
                    Register here
                </a>
            </p>
        </div>
    </form>
</div>
{% endblock %}
