#!/usr/bin/env python3
"""
Test the DELETE API directly to see if it works from Python
"""

import requests

# Configuration
PLEX_TOKEN = "KfQs3Gi6pfzbe4dJTHMU"
SERVER_ID = "8e7d7fd0bfe3b8bad6af94e6c28396aa4bfa1d15"

def test_delete_user():
    """Test deleting kevin_gagliano (shared_server_id: 39237796)"""
    headers = {"X-Plex-Token": PLEX_TOKEN}
    
    # First, get current shared servers to confirm the user exists
    get_url = f"https://plex.tv/api/servers/{SERVER_ID}/shared_servers"
    print(f"Getting shared servers from: {get_url}")
    
    r = requests.get(get_url, headers=headers)
    print(f"GET Status code: {r.status_code}")
    if r.status_code == 200:
        print("Current shared servers found")
        if "kevin_gagliano" in r.text:
            print("✅ kevin_gagliano found in shared servers")
        else:
            print("❌ kevin_gagliano NOT found in shared servers")
            return
    else:
        print(f"❌ GET failed: {r.text}")
        return
    
    # Now try to delete kevin_gagliano
    delete_url = f"https://plex.tv/api/servers/{SERVER_ID}/shared_servers/39237796"
    print(f"\nDeleting user from: {delete_url}")
    
    r = requests.delete(delete_url, headers=headers)
    print(f"DELETE Status code: {r.status_code}")
    print(f"DELETE Response: {r.text}")
    
    if r.status_code == 200:
        print("✅ DELETE successful!")
        
        # Verify the user was removed
        r = requests.get(get_url, headers=headers)
        if r.status_code == 200:
            if "kevin_gagliano" not in r.text:
                print("✅ User successfully removed from shared servers")
            else:
                print("❌ User still exists in shared servers")
        
    else:
        print(f"❌ DELETE failed: {r.status_code} - {r.text}")

if __name__ == "__main__":
    print("=== Testing Plex DELETE API ===")
    test_delete_user()
