"""
Subscription management service for handling user subscriptions and library sharing
"""

from datetime import datetime, timedelta
from flask import current_app
from app import db
from app.models import User, Subscription, SharedLibrary, Library
from app.services.plex_service import PlexService, PlexAPIError

class SubscriptionService:
    """Service for managing user subscriptions and library access"""
    
    def __init__(self, plex_server=None):
        self.plex_server = plex_server
        self.plex_service = PlexService(plex_server) if plex_server else None
    
    def create_subscription(self, user_id, end_date, start_date=None, plan_name=None, 
                          plan_duration_days=None, admin_id=None):
        """Create a new subscription for a user"""
        try:
            user = User.query.get(user_id)
            if not user:
                return False, "User not found"
            
            # Create subscription record
            subscription = Subscription(
                user_id=user_id,
                start_date=start_date or datetime.utcnow(),
                end_date=end_date,
                plan_name=plan_name,
                plan_duration_days=plan_duration_days,
                created_by_admin_id=admin_id
            )
            
            db.session.add(subscription)
            
            # Update user subscription info
            user.subscription_end_date = end_date
            user.subscription_status = 'active'
            
            # Share default libraries if this is a new subscription
            if not user.get_original_libraries():
                self._share_default_libraries(user)
            else:
                # Restore previously shared libraries
                self._restore_shared_libraries(user)
            
            db.session.commit()
            return True, "Subscription created successfully"
            
        except Exception as e:
            db.session.rollback()
            return False, f"Failed to create subscription: {str(e)}"
    
    def extend_subscription(self, user_id, days, admin_id=None):
        """Extend user subscription by specified days"""
        try:
            user = User.query.get(user_id)
            if not user:
                return False, "User not found"
            
            # Get current subscription or create new one
            current_subscription = user.subscriptions.filter_by(status='active').first()
            
            if current_subscription:
                current_subscription.extend_subscription(days)
                new_end_date = current_subscription.end_date
            else:
                # Create new subscription
                start_date = datetime.utcnow()
                new_end_date = start_date + timedelta(days=days)
                
                subscription = Subscription(
                    user_id=user_id,
                    start_date=start_date,
                    end_date=new_end_date,
                    plan_duration_days=days,
                    created_by_admin_id=admin_id
                )
                db.session.add(subscription)
            
            # Update user info
            user.subscription_end_date = new_end_date
            user.subscription_status = 'active'
            
            # Restore libraries if subscription was expired
            if user.get_subscription_status() == 'expired':
                self._restore_shared_libraries(user)
            
            db.session.commit()
            return True, f"Subscription extended by {days} days"
            
        except Exception as e:
            db.session.rollback()
            return False, f"Failed to extend subscription: {str(e)}"
    
    def cancel_subscription(self, user_id, admin_id=None):
        """Cancel user subscription and unshare libraries"""
        try:
            user = User.query.get(user_id)
            if not user:
                return False, "User not found"
            
            # Update subscription status
            current_subscription = user.subscriptions.filter_by(status='active').first()
            if current_subscription:
                current_subscription.cancel_subscription()
            
            # Update user status
            user.subscription_status = 'cancelled'
            
            # Unshare all libraries
            self._unshare_all_libraries(user)
            
            db.session.commit()
            return True, "Subscription cancelled successfully"
            
        except Exception as e:
            db.session.rollback()
            return False, f"Failed to cancel subscription: {str(e)}"
    
    def check_expired_subscriptions(self):
        """Check for expired subscriptions and unshare libraries"""
        try:
            expired_subscriptions = Subscription.get_expired()
            processed_count = 0
            
            for subscription in expired_subscriptions:
                user = subscription.user
                
                # Skip if already processed
                if user.subscription_status == 'expired':
                    continue
                
                # Update subscription status
                subscription.status = 'expired'
                user.subscription_status = 'expired'
                
                # Unshare libraries
                self._unshare_all_libraries(user)
                
                processed_count += 1
            
            db.session.commit()
            return True, f"Processed {processed_count} expired subscriptions"
            
        except Exception as e:
            db.session.rollback()
            return False, f"Failed to process expired subscriptions: {str(e)}"
    
    def get_expiring_subscriptions(self, days=7):
        """Get subscriptions expiring within specified days"""
        return Subscription.get_expiring_soon(days)
    
    def _share_default_libraries(self, user):
        """Share default libraries with user"""
        if not self.plex_server:
            return
        
        default_library_ids = self.plex_server.get_default_libraries()
        shared_libraries = []
        
        for lib_id in default_library_ids:
            library = Library.query.filter_by(
                plex_server_id=self.plex_server.id,
                plex_library_id=str(lib_id)
            ).first()
            
            if library:
                # Create shared library record
                shared_lib = SharedLibrary.query.filter_by(
                    user_id=user.id,
                    library_id=library.id
                ).first()
                
                if not shared_lib:
                    shared_lib = SharedLibrary(
                        user_id=user.id,
                        library_id=library.id,
                        is_shared=True
                    )
                    db.session.add(shared_lib)
                else:
                    shared_lib.share()
                
                shared_libraries.append(str(lib_id))
                
                # Share via Plex API
                if self.plex_service:
                    try:
                        self.plex_service.share_library_with_user(
                            user.plex_user_id, 
                            library.plex_library_id
                        )
                    except PlexAPIError:
                        pass  # Continue even if Plex API fails
        
        # Store original shared libraries
        user.set_original_libraries(shared_libraries)
    
    def _restore_shared_libraries(self, user):
        """Restore previously shared libraries for user"""
        original_libraries = user.get_original_libraries()
        
        for lib_id in original_libraries:
            library = Library.query.filter_by(
                plex_server_id=self.plex_server.id if self.plex_server else user.plex_server_id,
                plex_library_id=str(lib_id)
            ).first()
            
            if library:
                # Update shared library record
                shared_lib = SharedLibrary.query.filter_by(
                    user_id=user.id,
                    library_id=library.id
                ).first()
                
                if not shared_lib:
                    shared_lib = SharedLibrary(
                        user_id=user.id,
                        library_id=library.id,
                        is_shared=True
                    )
                    db.session.add(shared_lib)
                else:
                    shared_lib.share()
                
                # Share via Plex API
                if self.plex_service:
                    try:
                        self.plex_service.share_library_with_user(
                            user.plex_user_id, 
                            library.plex_library_id
                        )
                    except PlexAPIError:
                        pass  # Continue even if Plex API fails
    
    def _unshare_all_libraries(self, user):
        """Unshare all libraries from user using complete user removal"""
        # Store current shared libraries as original if not already stored
        if not user.get_original_libraries():
            current_shared = user.get_current_shared_libraries()
            user.set_original_libraries(current_shared)

        # Update all shared library records to unshared
        shared_libraries = SharedLibrary.query.filter_by(
            user_id=user.id,
            is_shared=True
        ).all()

        for shared_lib in shared_libraries:
            shared_lib.unshare()

        # Use complete user removal via Plex API (proven to work)
        if self.plex_service:
            try:
                # Get the shared_server_id for this user
                shared_server_id = self.plex_service._get_shared_server_id(user.plex_user_id)
                if shared_server_id:
                    # Remove user completely from Plex sharing
                    machine_id = self.plex_server.machine_identifier
                    url = f'https://plex.tv/api/servers/{machine_id}/shared_servers/{shared_server_id}'
                    self.plex_service._make_request('DELETE', url)
                    print(f"✅ Successfully removed user {user.username} (Plex ID: {user.plex_user_id}) from Plex sharing")
                else:
                    print(f"⚠️ User {user.username} (Plex ID: {user.plex_user_id}) not found in Plex shared servers")
            except PlexAPIError as e:
                print(f"❌ Failed to remove user {user.username} from Plex: {e}")
                # Continue anyway - local database will be updated
    
    def share_library_with_user(self, user_id, library_id, admin_id=None):
        """Share specific library with user"""
        try:
            user = User.query.get(user_id)
            library = Library.query.get(library_id)
            
            if not user or not library:
                return False, "User or library not found"
            
            # Check if user has active subscription
            if user.get_subscription_status() != 'active':
                return False, "User does not have an active subscription"
            
            # Create or update shared library record
            shared_lib = SharedLibrary.query.filter_by(
                user_id=user_id,
                library_id=library_id
            ).first()
            
            if not shared_lib:
                shared_lib = SharedLibrary(
                    user_id=user_id,
                    library_id=library_id,
                    is_shared=True,
                    shared_by_admin_id=admin_id
                )
                db.session.add(shared_lib)
            else:
                shared_lib.share(admin_id)
            
            # Share via Plex API
            if self.plex_service:
                success, message = self.plex_service.share_library_with_user(
                    user.plex_user_id,
                    library.plex_library_id
                )
                if not success:
                    db.session.rollback()
                    return False, f"Failed to share via Plex: {message}"
            
            db.session.commit()
            return True, "Library shared successfully"
            
        except Exception as e:
            db.session.rollback()
            return False, f"Failed to share library: {str(e)}"
    
    def unshare_library_from_user(self, user_id, library_id):
        """Unshare specific library from user"""
        try:
            user = User.query.get(user_id)
            library = Library.query.get(library_id)
            
            if not user or not library:
                return False, "User or library not found"
            
            # Update shared library record
            shared_lib = SharedLibrary.query.filter_by(
                user_id=user_id,
                library_id=library_id
            ).first()
            
            if shared_lib:
                shared_lib.unshare()
            
            # Unshare via Plex API
            if self.plex_service:
                success, message = self.plex_service.unshare_library_from_user(
                    user.plex_user_id,
                    library.plex_library_id
                )
                if not success:
                    db.session.rollback()
                    return False, f"Failed to unshare via Plex: {message}"
            
            db.session.commit()
            return True, "Library unshared successfully"
            
        except Exception as e:
            db.session.rollback()
            return False, f"Failed to unshare library: {str(e)}"
