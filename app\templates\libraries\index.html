{% extends "base.html" %}

{% block title %}Libraries - Plex User Subscription Manager{% endblock %}
{% block page_title %}Libraries{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg leading-6 font-medium text-gray-900">Plex Libraries</h3>
                    <p class="mt-1 text-sm text-gray-600">
                        Manage your Plex libraries and their sharing settings.
                    </p>
                </div>
                <button onclick="syncLibraries()" 
                        class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-plex-600 hover:bg-plex-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-plex-500">
                    <i class="fas fa-sync-alt -ml-1 mr-2 h-4 w-4"></i>
                    Sync Libraries
                </button>
            </div>
        </div>
    </div>

    <!-- Libraries Grid -->
    {% if libraries %}
    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {% for library in libraries %}
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        {% if library.type == 'movie' %}
                        <i class="fas fa-film h-8 w-8 text-blue-500"></i>
                        {% elif library.type == 'show' %}
                        <i class="fas fa-tv h-8 w-8 text-green-500"></i>
                        {% elif library.type == 'artist' %}
                        <i class="fas fa-music h-8 w-8 text-purple-500"></i>
                        {% elif library.type == 'photo' %}
                        <i class="fas fa-images h-8 w-8 text-yellow-500"></i>
                        {% else %}
                        <i class="fas fa-folder h-8 w-8 text-gray-500"></i>
                        {% endif %}
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">{{ library.title }}</dt>
                            <dd class="flex items-center text-lg font-medium text-gray-900">
                                <span class="capitalize">{{ library.type }}</span>
                                {% if library.is_shared_by_default %}
                                <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    Default
                                </span>
                                {% endif %}
                            </dd>
                        </dl>
                    </div>
                </div>
                
                <div class="mt-5">
                    <div class="flex items-center justify-between text-sm text-gray-500">
                        <span>Shared with users</span>
                        <span class="font-medium">{{ library.shared_count }}/{{ library.total_users }}</span>
                    </div>
                    <div class="mt-2 w-full bg-gray-200 rounded-full h-2">
                        {% set percentage = (library.shared_count / library.total_users * 100) if library.total_users > 0 else 0 %}
                        <div class="bg-plex-600 h-2 rounded-full" style="width: {{ percentage }}%"></div>
                    </div>
                </div>
                
                <div class="mt-5 flex justify-between items-center">
                    <div class="text-xs text-gray-400">
                        ID: {{ library.plex_library_id }}
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="viewLibraryUsers({{ library.id }})" 
                                class="text-plex-600 hover:text-plex-900 text-sm font-medium">
                            View Users
                        </button>
                        <button onclick="toggleDefault({{ library.id }}, {{ library.is_shared_by_default|lower }})" 
                                class="text-gray-600 hover:text-gray-900 text-sm font-medium">
                            {% if library.is_shared_by_default %}Remove Default{% else %}Make Default{% endif %}
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="bg-white shadow rounded-lg">
        <div class="text-center py-12">
            <i class="fas fa-folder-open h-12 w-12 text-gray-400 mx-auto mb-4"></i>
            <h3 class="text-sm font-medium text-gray-900">No Libraries Found</h3>
            <p class="text-sm text-gray-500 mt-1">
                No libraries were found on your Plex server. 
                Make sure your server is configured correctly.
            </p>
            <div class="mt-4">
                <button onclick="syncLibraries()" 
                        class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-plex-600 hover:bg-plex-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-plex-500">
                    <i class="fas fa-sync-alt -ml-1 mr-2 h-4 w-4"></i>
                    Sync Libraries
                </button>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Statistics -->
    {% if libraries %}
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Library Statistics</h3>
            
            <dl class="grid grid-cols-1 gap-5 sm:grid-cols-3">
                <div class="px-4 py-5 bg-gray-50 shadow rounded-lg overflow-hidden sm:p-6">
                    <dt class="text-sm font-medium text-gray-500 truncate">Total Libraries</dt>
                    <dd class="mt-1 text-3xl font-semibold text-gray-900">{{ libraries|length }}</dd>
                </div>
                
                <div class="px-4 py-5 bg-gray-50 shadow rounded-lg overflow-hidden sm:p-6">
                    <dt class="text-sm font-medium text-gray-500 truncate">Default Libraries</dt>
                    <dd class="mt-1 text-3xl font-semibold text-gray-900">
                        {{ libraries|selectattr('is_shared_by_default')|list|length }}
                    </dd>
                </div>
                
                <div class="px-4 py-5 bg-gray-50 shadow rounded-lg overflow-hidden sm:p-6">
                    <dt class="text-sm font-medium text-gray-500 truncate">Library Types</dt>
                    <dd class="mt-1 text-3xl font-semibold text-gray-900">
                        {{ libraries|map(attribute='type')|unique|list|length }}
                    </dd>
                </div>
            </dl>
        </div>
    </div>
    {% endif %}
</div>

<!-- Library Users Modal -->
<div id="libraryUsersModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900" id="modalTitle">Library Users</h3>
                <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times h-6 w-6"></i>
                </button>
            </div>
            <div id="modalContent" class="max-h-96 overflow-y-auto">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<script>
async function syncLibraries() {
    try {
        const response = await fetch('/admin/api/sync-libraries', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            PlexApp.utils.showNotification(data.message, 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            PlexApp.utils.showNotification('Sync failed: ' + data.message, 'error');
        }
    } catch (error) {
        PlexApp.utils.showNotification('Sync failed: ' + error.message, 'error');
    }
}

async function toggleDefault(libraryId, isCurrentlyDefault) {
    const action = isCurrentlyDefault ? 'remove from' : 'add to';
    if (!confirm(`Are you sure you want to ${action} default libraries?`)) {
        return;
    }
    
    try {
        // This would need to be implemented as an API endpoint
        PlexApp.utils.showNotification('Feature coming soon!', 'info');
    } catch (error) {
        PlexApp.utils.showNotification('Failed to update library: ' + error.message, 'error');
    }
}

async function viewLibraryUsers(libraryId) {
    try {
        // This would need to be implemented as an API endpoint
        document.getElementById('modalTitle').textContent = 'Library Users';
        document.getElementById('modalContent').innerHTML = '<p class="text-center py-4">Loading users...</p>';
        document.getElementById('libraryUsersModal').classList.remove('hidden');
        
        // Placeholder content
        setTimeout(() => {
            document.getElementById('modalContent').innerHTML = '<p class="text-center py-4 text-gray-500">Feature coming soon!</p>';
        }, 500);
    } catch (error) {
        PlexApp.utils.showNotification('Failed to load users: ' + error.message, 'error');
    }
}

function closeModal() {
    document.getElementById('libraryUsersModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('libraryUsersModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});
</script>
{% endblock %}
