{% extends "base.html" %}

{% block title %}Dashboard - Plex User Subscription Manager{% endblock %}
{% block page_title %}Dashboard{% endblock %}

{% block content %}
<div x-data="dashboardData()" x-init="init()">
    <!-- Stats Overview -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        <!-- Total Users -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-users h-6 w-6 text-gray-400"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Users</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ stats.total_users }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Subscriptions -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle h-6 w-6 text-green-400"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Active</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ stats.active_users }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Expiring Soon -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle h-6 w-6 text-yellow-400"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Expiring Soon</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ stats.expiring_soon }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Expired -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-times-circle h-6 w-6 text-red-400"></i>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Expired</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ stats.expired_users }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Quick Actions</h3>
            <div class="flex flex-wrap gap-3">
                <button @click="syncPlexData()" 
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-plex-600 hover:bg-plex-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-plex-500"
                        :disabled="syncing"
                        :class="{ 'btn-loading': syncing }">
                    <i class="fas fa-sync-alt -ml-1 mr-2 h-4 w-4"></i>
                    <span x-show="!syncing">Sync Plex Data</span>
                    <span x-show="syncing" x-cloak>Syncing...</span>
                </button>
                
                <button @click="checkExpiredSubscriptions()" 
                        class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-plex-500"
                        :disabled="checking"
                        :class="{ 'btn-loading': checking }">
                    <i class="fas fa-clock -ml-1 mr-2 h-4 w-4"></i>
                    <span x-show="!checking">Check Expired</span>
                    <span x-show="checking" x-cloak>Checking...</span>
                </button>
                
                <a href="{{ url_for('main.users') }}" 
                   class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-plex-500">
                    <i class="fas fa-users -ml-1 mr-2 h-4 w-4"></i>
                    Manage Users
                </a>
            </div>
        </div>
    </div>

    <!-- Expiring Subscriptions -->
    {% if expiring_subscriptions %}
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                <i class="fas fa-exclamation-triangle text-yellow-500 mr-2"></i>
                Expiring Subscriptions
            </h3>
            <div class="space-y-3">
                {% for item in expiring_subscriptions %}
                <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                    <div class="flex items-center space-x-3">
                        <div class="h-8 w-8 rounded-full bg-yellow-500 flex items-center justify-center">
                            <span class="text-sm font-medium text-white">{{ item.user.username[0].upper() }}</span>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-900">{{ item.user.username }}</p>
                            <p class="text-sm text-gray-500">{{ item.user.email or 'No email' }}</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <div id="countdown-{{ item.user.id }}" 
                             data-countdown="true" 
                             data-user-id="{{ item.user.id }}" 
                             data-end-date="{{ item.user.subscription_end_date.isoformat() }}"
                             class="text-sm font-medium text-yellow-800">
                            {{ item.days_left }} day{{ 's' if item.days_left != 1 else '' }} left
                        </div>
                        <div class="mt-1">
                            <button @click="extendSubscription({{ item.user.id }})" 
                                    class="text-xs text-plex-600 hover:text-plex-500">
                                Extend
                            </button>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Recent Activity -->
    {% if recent_subscriptions %}
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Activity</h3>
            <div class="flow-root">
                <ul role="list" class="-mb-8">
                    {% for subscription in recent_subscriptions %}
                    <li>
                        <div class="relative pb-8">
                            {% if not loop.last %}
                            <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"></span>
                            {% endif %}
                            <div class="relative flex space-x-3">
                                <div>
                                    <span class="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center ring-8 ring-white">
                                        <i class="fas fa-plus h-4 w-4 text-white"></i>
                                    </span>
                                </div>
                                <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                                    <div>
                                        <p class="text-sm text-gray-500">
                                            Subscription created for <span class="font-medium text-gray-900">{{ subscription.user.username }}</span>
                                        </p>
                                    </div>
                                    <div class="text-right text-sm whitespace-nowrap text-gray-500">
                                        {{ subscription.created_at.strftime('%m/%d %H:%M') }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script>
function dashboardData() {
    return {
        syncing: false,
        checking: false,
        
        init() {
            // Auto-refresh stats every 30 seconds
            setInterval(() => {
                this.refreshStats();
            }, 30000);
        },
        
        async syncPlexData() {
            this.syncing = true;
            try {
                const response = await PlexApp.utils.apiRequest('/api/sync-plex-data', {
                    method: 'POST'
                });
                PlexApp.utils.showNotification(response.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } catch (error) {
                PlexApp.utils.showNotification(error.message, 'error');
            } finally {
                this.syncing = false;
            }
        },
        
        async checkExpiredSubscriptions() {
            this.checking = true;
            try {
                const response = await PlexApp.utils.apiRequest('/api/check-expired-subscriptions', {
                    method: 'POST'
                });
                PlexApp.utils.showNotification(response.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } catch (error) {
                PlexApp.utils.showNotification(error.message, 'error');
            } finally {
                this.checking = false;
            }
        },
        
        async extendSubscription(userId) {
            const days = prompt('How many days to extend?', '30');
            if (days && !isNaN(days) && parseInt(days) > 0) {
                try {
                    await PlexApp.subscription.extend(userId, parseInt(days));
                    setTimeout(() => location.reload(), 1000);
                } catch (error) {
                    // Error already handled by PlexApp.subscription.extend
                }
            }
        },
        
        async refreshStats() {
            try {
                const stats = await PlexApp.utils.apiRequest('/api/dashboard/stats');
                // Update stats in the UI (would need to make stats reactive)
            } catch (error) {
                console.error('Failed to refresh stats:', error);
            }
        }
    }
}
</script>
{% endblock %}
