{% extends "base.html" %}

{% block title %}Change Password - Plex User Subscription Manager{% endblock %}
{% block page_title %}Change Password{% endblock %}

{% block content %}
<div class="max-w-md mx-auto">
    <div class="bg-white shadow rounded-lg p-6">
        <div class="mb-6">
            <h3 class="text-lg font-medium text-gray-900">Change Password</h3>
            <p class="mt-1 text-sm text-gray-600">
                Update your account password for security.
            </p>
        </div>
        
        <form method="POST" x-data="{ loading: false }" @submit="loading = true">
    
            
            <div class="space-y-4">
                <div>
                    <label for="current_password" class="block text-sm font-medium text-gray-700">
                        Current Password
                    </label>
                    <input type="password" name="current_password" id="current_password" required
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-plex-500 focus:border-plex-500 sm:text-sm">
                </div>
                
                <div>
                    <label for="new_password" class="block text-sm font-medium text-gray-700">
                        New Password
                    </label>
                    <input type="password" name="new_password" id="new_password" required
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-plex-500 focus:border-plex-500 sm:text-sm">
                </div>
                
                <div>
                    <label for="confirm_password" class="block text-sm font-medium text-gray-700">
                        Confirm New Password
                    </label>
                    <input type="password" name="confirm_password" id="confirm_password" required
                           class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-plex-500 focus:border-plex-500 sm:text-sm">
                </div>
            </div>
            
            <div class="mt-6 flex items-center justify-end space-x-3">
                <a href="{{ url_for('main.dashboard') }}" 
                   class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-plex-500">
                    Cancel
                </a>
                <button type="submit" 
                        class="bg-plex-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-plex-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-plex-500 disabled:opacity-50"
                        :disabled="loading"
                        :class="{ 'btn-loading': loading }">
                    <span x-show="!loading">Change Password</span>
                    <span x-show="loading" x-cloak>Changing...</span>
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
