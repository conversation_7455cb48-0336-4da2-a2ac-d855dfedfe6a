from datetime import datetime, timed<PERSON>ta
from flask_sqlalchemy import SQLAlchemy
from app import db

class Subscription(db.Model):
    """Subscription history and management for users"""
    __tablename__ = 'subscriptions'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>('users.id'), nullable=False)
    
    # Subscription details
    start_date = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    end_date = db.Column(db.DateTime, nullable=False)
    status = db.Column(db.String(20), default='active', nullable=False)  # active, expired, cancelled, suspended
    
    # Subscription metadata
    plan_name = db.Column(db.String(100))
    plan_duration_days = db.Column(db.Integer)
    price = db.Column(db.Numeric(10, 2))
    currency = db.Column(db.String(3), default='USD')
    
    # Administrative information
    created_by_admin_id = db.Column(db.<PERSON>te<PERSON>, db.<PERSON><PERSON>('admins.id'))
    notes = db.Column(db.Text)
    
    # Auto-renewal settings
    auto_renew = db.Column(db.Boolean, default=False)
    renewal_duration_days = db.Column(db.Integer)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __init__(self, user_id, end_date, start_date=None, plan_name=None, 
                 plan_duration_days=None, created_by_admin_id=None):
        self.user_id = user_id
        self.start_date = start_date or datetime.utcnow()
        self.end_date = end_date
        self.plan_name = plan_name
        self.plan_duration_days = plan_duration_days
        self.created_by_admin_id = created_by_admin_id
    
    def is_unlimited(self):
        """Check if subscription is unlimited (end date is far in future)"""
        # Consider unlimited if end date is more than 50 years from now
        far_future = datetime.utcnow() + timedelta(days=18250)  # ~50 years
        return self.end_date >= far_future

    def is_active(self):
        """Check if subscription is currently active"""
        now = datetime.utcnow()
        return (self.status == 'active' and
                self.start_date <= now and
                (self.is_unlimited() or now <= self.end_date))

    def is_expired(self):
        """Check if subscription has expired"""
        if self.is_unlimited():
            return False
        return datetime.utcnow() > self.end_date
    
    def days_remaining(self):
        """Get number of days remaining in subscription"""
        if self.is_unlimited():
            return float('inf')  # Unlimited
        if self.is_expired():
            return 0

        now = datetime.utcnow()
        delta = self.end_date - now
        return max(0, delta.days)
    
    def hours_remaining(self):
        """Get number of hours remaining in subscription"""
        if self.is_expired():
            return 0
        
        now = datetime.utcnow()
        delta = self.end_date - now
        return max(0, int(delta.total_seconds() / 3600))
    
    def progress_percentage(self):
        """Get subscription progress as percentage (0-100)"""
        total_duration = self.end_date - self.start_date
        elapsed = datetime.utcnow() - self.start_date
        
        if elapsed.total_seconds() <= 0:
            return 0
        elif elapsed >= total_duration:
            return 100
        else:
            return int((elapsed.total_seconds() / total_duration.total_seconds()) * 100)
    
    def extend_subscription(self, days):
        """Extend subscription by specified number of days"""
        self.end_date = self.end_date + timedelta(days=days)
        self.updated_at = datetime.utcnow()
        
        # Reactivate if was expired
        if self.status == 'expired':
            self.status = 'active'
    
    def cancel_subscription(self):
        """Cancel the subscription"""
        self.status = 'cancelled'
        self.updated_at = datetime.utcnow()
    
    def suspend_subscription(self):
        """Suspend the subscription"""
        self.status = 'suspended'
        self.updated_at = datetime.utcnow()
    
    def reactivate_subscription(self):
        """Reactivate a cancelled or suspended subscription"""
        if not self.is_expired():
            self.status = 'active'
            self.updated_at = datetime.utcnow()
    
    def renew_subscription(self, duration_days=None):
        """Renew subscription for specified duration"""
        duration = duration_days or self.renewal_duration_days or self.plan_duration_days or 30
        
        # If subscription is expired, start from now
        if self.is_expired():
            self.start_date = datetime.utcnow()
            self.end_date = self.start_date + timedelta(days=duration)
        else:
            # Extend from current end date
            self.end_date = self.end_date + timedelta(days=duration)
        
        self.status = 'active'
        self.updated_at = datetime.utcnow()
    
    @classmethod
    def get_expiring_soon(cls, days=7):
        """Get subscriptions expiring within specified days"""
        cutoff_date = datetime.utcnow() + timedelta(days=days)
        return cls.query.filter(
            cls.status == 'active',
            cls.end_date <= cutoff_date,
            cls.end_date > datetime.utcnow()
        ).all()
    
    @classmethod
    def get_expired(cls):
        """Get all expired subscriptions"""
        return cls.query.filter(
            cls.end_date < datetime.utcnow(),
            cls.status.in_(['active', 'expired'])
        ).all()
    
    def __repr__(self):
        return f'<Subscription User:{self.user_id} {self.start_date} - {self.end_date} ({self.status})>'
