"""
Basic application tests for Plex User Subscription Manager
"""

import unittest
import tempfile
import os
from app import create_app, db
from app.models import Admin, PlexServer, User, Library, Subscription

class PlexAppTestCase(unittest.TestCase):
    """Base test case for the Plex application"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.db_fd, self.db_path = tempfile.mkstemp()
        self.app = create_app('testing')
        self.app.config['DATABASE_URL'] = f'sqlite:///{self.db_path}'
        self.app.config['TESTING'] = True
        self.app.config['WTF_CSRF_ENABLED'] = False
        
        self.client = self.app.test_client()
        
        with self.app.app_context():
            db.create_all()
            
            # Create test admin
            self.admin = Admin(
                username='testadmin',
                email='<EMAIL>',
                password='testpass',
                is_master=True
            )
            db.session.add(self.admin)
            db.session.commit()
    
    def tearDown(self):
        """Clean up test fixtures"""
        with self.app.app_context():
            db.session.remove()
            db.drop_all()
        os.close(self.db_fd)
        os.unlink(self.db_path)
    
    def login(self, username='testadmin', password='testpass'):
        """Helper method to log in"""
        return self.client.post('/auth/login', data={
            'username': username,
            'password': password
        }, follow_redirects=True)
    
    def logout(self):
        """Helper method to log out"""
        return self.client.get('/auth/logout', follow_redirects=True)

class AuthTestCase(PlexAppTestCase):
    """Test authentication functionality"""
    
    def test_login_page(self):
        """Test login page loads"""
        rv = self.client.get('/auth/login')
        self.assertEqual(rv.status_code, 200)
        self.assertIn(b'Sign in to your account', rv.data)
    
    def test_valid_login(self):
        """Test valid login"""
        rv = self.login()
        self.assertEqual(rv.status_code, 200)
        # Should redirect to dashboard after login
    
    def test_invalid_login(self):
        """Test invalid login"""
        rv = self.login('invalid', 'invalid')
        self.assertEqual(rv.status_code, 200)
        self.assertIn(b'Invalid username or password', rv.data)
    
    def test_logout(self):
        """Test logout"""
        self.login()
        rv = self.logout()
        self.assertEqual(rv.status_code, 200)
        self.assertIn(b'You have been logged out', rv.data)

class DashboardTestCase(PlexAppTestCase):
    """Test dashboard functionality"""
    
    def test_dashboard_requires_login(self):
        """Test dashboard requires authentication"""
        rv = self.client.get('/')
        self.assertEqual(rv.status_code, 302)  # Redirect to login
    
    def test_dashboard_with_login(self):
        """Test dashboard with valid login"""
        self.login()
        rv = self.client.get('/')
        self.assertEqual(rv.status_code, 200)

class ModelTestCase(PlexAppTestCase):
    """Test database models"""
    
    def test_admin_creation(self):
        """Test admin model creation"""
        with self.app.app_context():
            admin = Admin(
                username='newadmin',
                email='<EMAIL>',
                password='newpass'
            )
            db.session.add(admin)
            db.session.commit()
            
            # Verify admin was created
            found_admin = Admin.query.filter_by(username='newadmin').first()
            self.assertIsNotNone(found_admin)
            self.assertEqual(found_admin.email, '<EMAIL>')
            self.assertTrue(found_admin.check_password('newpass'))
    
    def test_plex_server_creation(self):
        """Test Plex server model creation"""
        with self.app.app_context():
            server = PlexServer(
                admin_id=self.admin.id,
                name='Test Server',
                url='http://localhost:32400',
                token='test-token'
            )
            db.session.add(server)
            db.session.commit()
            
            # Verify server was created
            found_server = PlexServer.query.filter_by(name='Test Server').first()
            self.assertIsNotNone(found_server)
            self.assertEqual(found_server.url, 'http://localhost:32400')
            self.assertEqual(found_server.get_token(), 'test-token')
    
    def test_user_subscription_status(self):
        """Test user subscription status logic"""
        with self.app.app_context():
            # Create Plex server
            server = PlexServer(
                admin_id=self.admin.id,
                name='Test Server',
                url='http://localhost:32400',
                token='test-token'
            )
            db.session.add(server)
            db.session.commit()
            
            # Create user
            user = User(
                plex_server_id=server.id,
                plex_user_id='test-user-1',
                username='testuser',
                email='<EMAIL>'
            )
            db.session.add(user)
            db.session.commit()
            
            # Test no subscription
            self.assertEqual(user.get_subscription_status(), 'no_subscription')
            
            # Test active subscription
            from datetime import datetime, timedelta
            user.subscription_end_date = datetime.utcnow() + timedelta(days=30)
            self.assertEqual(user.get_subscription_status(), 'active')
            self.assertEqual(user.days_until_expiry(), 30)
            
            # Test expired subscription
            user.subscription_end_date = datetime.utcnow() - timedelta(days=1)
            self.assertEqual(user.get_subscription_status(), 'expired')
            self.assertEqual(user.days_until_expiry(), 0)

class APITestCase(PlexAppTestCase):
    """Test API endpoints"""
    
    def setUp(self):
        """Set up API test fixtures"""
        super().setUp()
        with self.app.app_context():
            # Create Plex server
            self.server = PlexServer(
                admin_id=self.admin.id,
                name='Test Server',
                url='http://localhost:32400',
                token='test-token'
            )
            db.session.add(self.server)
            
            # Create test user
            self.user = User(
                plex_server_id=self.server.id,
                plex_user_id='test-user-1',
                username='testuser',
                email='<EMAIL>'
            )
            db.session.add(self.user)
            db.session.commit()
    
    def test_api_requires_login(self):
        """Test API endpoints require authentication"""
        rv = self.client.get('/api/dashboard/stats')
        self.assertEqual(rv.status_code, 302)  # Redirect to login
    
    def test_dashboard_stats_api(self):
        """Test dashboard stats API"""
        self.login()
        rv = self.client.get('/api/dashboard/stats')
        self.assertEqual(rv.status_code, 200)
        
        import json
        data = json.loads(rv.data)
        self.assertIn('total_users', data)
        self.assertIn('active_users', data)
        self.assertIn('expired_users', data)
        self.assertIn('expiring_soon', data)
    
    def test_extend_subscription_api(self):
        """Test extend subscription API"""
        self.login()
        rv = self.client.post(f'/api/users/{self.user.id}/extend-subscription',
                             json={'days': 30},
                             content_type='application/json')
        self.assertEqual(rv.status_code, 200)
        
        import json
        data = json.loads(rv.data)
        self.assertTrue(data['success'])
        self.assertIn('message', data)

if __name__ == '__main__':
    unittest.main()
