{% extends "base.html" %}

{% block title %}Settings - Plex User Subscription Manager{% endblock %}
{% block page_title %}Settings{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Plex Server Status -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Plex Server Status</h3>
            
            {% if plex_server %}
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-sm font-medium text-gray-900">{{ plex_server.name }}</p>
                    <p class="text-sm text-gray-500">{{ plex_server.url }}</p>
                    {% if plex_server.last_sync %}
                    <p class="text-xs text-gray-400">Last synced: {{ plex_server.last_sync.strftime('%Y-%m-%d %H:%M') }}</p>
                    {% endif %}
                </div>
                <div class="flex items-center space-x-2">
                    <div class="h-3 w-3 bg-green-400 rounded-full"></div>
                    <span class="text-sm text-gray-500">Connected</span>
                </div>
            </div>
            
            <div class="mt-4 flex space-x-3">
                <a href="{{ url_for('admin.plex_setup') }}" 
                   class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-plex-500">
                    <i class="fas fa-edit -ml-0.5 mr-2 h-4 w-4"></i>
                    Edit Configuration
                </a>
                
                <button onclick="testConnection()" 
                        class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-plex-500">
                    <i class="fas fa-plug -ml-0.5 mr-2 h-4 w-4"></i>
                    Test Connection
                </button>
            </div>
            {% else %}
            <div class="text-center py-6">
                <i class="fas fa-server h-12 w-12 text-gray-400 mx-auto mb-4"></i>
                <h3 class="text-sm font-medium text-gray-900">No Plex Server Configured</h3>
                <p class="text-sm text-gray-500 mt-1">Set up your Plex server to get started.</p>
                <div class="mt-4">
                    <a href="{{ url_for('admin.plex_setup') }}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-plex-600 hover:bg-plex-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-plex-500">
                        <i class="fas fa-plus -ml-1 mr-2 h-4 w-4"></i>
                        Configure Plex Server
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Quick Actions</h3>
            
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <a href="{{ url_for('admin.library_settings') }}" 
                   class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm flex items-center space-x-3 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-plex-500">
                    <div class="flex-shrink-0">
                        <i class="fas fa-share-alt h-6 w-6 text-plex-600"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <span class="absolute inset-0"></span>
                        <p class="text-sm font-medium text-gray-900">Library Settings</p>
                        <p class="text-sm text-gray-500 truncate">Configure default shared libraries</p>
                    </div>
                </a>

                {% if current_user.is_master %}
                <a href="{{ url_for('admin.manage_admins') }}" 
                   class="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm flex items-center space-x-3 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-plex-500">
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-shield h-6 w-6 text-plex-600"></i>
                    </div>
                    <div class="flex-1 min-w-0">
                        <span class="absolute inset-0"></span>
                        <p class="text-sm font-medium text-gray-900">Manage Admins</p>
                        <p class="text-sm text-gray-500 truncate">Add and manage administrator accounts</p>
                    </div>
                </a>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Account Information -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Account Information</h3>
            
            <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                <div>
                    <dt class="text-sm font-medium text-gray-500">Username</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ current_user.username }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Email</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ current_user.email }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Role</dt>
                    <dd class="mt-1 text-sm text-gray-900">
                        {% if current_user.is_master %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                            Master Administrator
                        </span>
                        {% else %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Administrator
                        </span>
                        {% endif %}
                    </dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Last Login</dt>
                    <dd class="mt-1 text-sm text-gray-900">
                        {% if current_user.last_login %}
                        {{ current_user.last_login.strftime('%Y-%m-%d %H:%M') }}
                        {% else %}
                        Never
                        {% endif %}
                    </dd>
                </div>
            </dl>
            
            <div class="mt-6">
                <a href="{{ url_for('auth.change_password') }}" 
                   class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-plex-500">
                    <i class="fas fa-key -ml-1 mr-2 h-4 w-4"></i>
                    Change Password
                </a>
            </div>
        </div>
    </div>
</div>

<script>
async function testConnection() {
    try {
        const response = await fetch('/admin/api/test-plex-token', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                token: '{{ plex_server.get_token() if plex_server else "" }}'
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            PlexApp.utils.showNotification('Connection test successful!', 'success');
        } else {
            PlexApp.utils.showNotification('Connection test failed: ' + data.message, 'error');
        }
    } catch (error) {
        PlexApp.utils.showNotification('Connection test failed: ' + error.message, 'error');
    }
}
</script>
{% endblock %}
