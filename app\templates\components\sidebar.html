<!-- Sidebar component -->
<div class="flex grow flex-col gap-y-5 overflow-y-auto bg-gray-900 px-6 pb-4">
    <div class="flex h-16 shrink-0 items-center">
        <div class="flex items-center space-x-3">
            <div class="h-8 w-8 rounded bg-plex-500 flex items-center justify-center">
                <i class="fas fa-play text-white text-sm"></i>
            </div>
            <span class="text-white font-semibold text-lg">Plex Manager</span>
        </div>
    </div>
    
    <nav class="flex flex-1 flex-col">
        <ul role="list" class="flex flex-1 flex-col gap-y-7">
            <li>
                <ul role="list" class="-mx-2 space-y-1">
                    <li>
                        <a href="{{ url_for('main.dashboard') }}" 
                           class="group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold {{ 'bg-gray-800 text-white' if request.endpoint == 'main.dashboard' else 'text-gray-400 hover:text-white hover:bg-gray-800' }}">
                            <i class="fas fa-chart-line h-6 w-6 shrink-0"></i>
                            Dashboard
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('main.users') }}" 
                           class="group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold {{ 'bg-gray-800 text-white' if request.endpoint == 'main.users' else 'text-gray-400 hover:text-white hover:bg-gray-800' }}">
                            <i class="fas fa-users h-6 w-6 shrink-0"></i>
                            Users
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('main.libraries') }}" 
                           class="group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold {{ 'bg-gray-800 text-white' if request.endpoint == 'main.libraries' else 'text-gray-400 hover:text-white hover:bg-gray-800' }}">
                            <i class="fas fa-folder h-6 w-6 shrink-0"></i>
                            Libraries
                        </a>
                    </li>
                </ul>
            </li>
            
            <li>
                <div class="text-xs font-semibold leading-6 text-gray-400">Settings</div>
                <ul role="list" class="-mx-2 mt-2 space-y-1">
                    <li>
                        <a href="{{ url_for('admin.settings') }}" 
                           class="group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold {{ 'bg-gray-800 text-white' if request.endpoint == 'admin.settings' else 'text-gray-400 hover:text-white hover:bg-gray-800' }}">
                            <i class="fas fa-cog h-6 w-6 shrink-0"></i>
                            General
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('admin.plex_setup') }}" 
                           class="group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold {{ 'bg-gray-800 text-white' if request.endpoint == 'admin.plex_setup' else 'text-gray-400 hover:text-white hover:bg-gray-800' }}">
                            <i class="fas fa-server h-6 w-6 shrink-0"></i>
                            Plex Server
                        </a>
                    </li>
                    <li>
                        <a href="{{ url_for('admin.library_settings') }}" 
                           class="group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold {{ 'bg-gray-800 text-white' if request.endpoint == 'admin.library_settings' else 'text-gray-400 hover:text-white hover:bg-gray-800' }}">
                            <i class="fas fa-share-alt h-6 w-6 shrink-0"></i>
                            Library Sharing
                        </a>
                    </li>
                    {% if current_user.is_master %}
                    <li>
                        <a href="{{ url_for('admin.manage_admins') }}" 
                           class="group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold {{ 'bg-gray-800 text-white' if request.endpoint == 'admin.manage_admins' else 'text-gray-400 hover:text-white hover:bg-gray-800' }}">
                            <i class="fas fa-user-shield h-6 w-6 shrink-0"></i>
                            Manage Admins
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </li>
            
            <li class="mt-auto">
                <div class="group -mx-2 flex gap-x-3 rounded-md p-2 text-sm font-semibold leading-6 text-gray-400">
                    <i class="fas fa-info-circle h-6 w-6 shrink-0"></i>
                    <div>
                        <div class="text-xs">Server Status</div>
                        <div class="flex items-center space-x-2 mt-1">
                            <div class="h-2 w-2 rounded-full bg-green-500"></div>
                            <span class="text-xs">Online</span>
                        </div>
                    </div>
                </div>
            </li>
        </ul>
    </nav>
</div>
