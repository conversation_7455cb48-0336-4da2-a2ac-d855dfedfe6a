{% extends "base.html" %}

{% block title %}Library Settings - Plex User Subscription Manager{% endblock %}
{% block page_title %}Library Sharing Settings{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-2">Default Library Sharing</h3>
            <p class="text-sm text-gray-600">
                Select which libraries should be shared with new users by default. 
                These settings will apply to all new subscriptions.
            </p>
        </div>
    </div>

    <!-- Library Settings Form -->
    <div class="bg-white shadow rounded-lg">
        <form method="POST" x-data="{ loading: false }" @submit="loading = true">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
            
            <div class="px-4 py-5 sm:p-6">
                {% if libraries %}
                <div class="space-y-4">
                    {% for library in libraries %}
                    <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <div class="flex-shrink-0">
                                {% if library.type == 'movie' %}
                                <i class="fas fa-film h-6 w-6 text-blue-500"></i>
                                {% elif library.type == 'show' %}
                                <i class="fas fa-tv h-6 w-6 text-green-500"></i>
                                {% elif library.type == 'artist' %}
                                <i class="fas fa-music h-6 w-6 text-purple-500"></i>
                                {% elif library.type == 'photo' %}
                                <i class="fas fa-images h-6 w-6 text-yellow-500"></i>
                                {% else %}
                                <i class="fas fa-folder h-6 w-6 text-gray-500"></i>
                                {% endif %}
                            </div>
                            <div>
                                <h4 class="text-sm font-medium text-gray-900">{{ library.title }}</h4>
                                <p class="text-sm text-gray-500 capitalize">{{ library.type }}</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-4">
                            <div class="text-sm text-gray-500">
                                Shared with {{ library.get_shared_users_count() }} users
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" name="default_libraries" value="{{ library.plex_library_id }}"
                                       {% if library.plex_library_id in default_library_ids %}checked{% endif %}
                                       class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-plex-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-plex-600"></div>
                                <span class="ml-3 text-sm font-medium text-gray-700">Default</span>
                            </label>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-12">
                    <i class="fas fa-folder-open h-12 w-12 text-gray-400 mx-auto mb-4"></i>
                    <h3 class="text-sm font-medium text-gray-900">No Libraries Found</h3>
                    <p class="text-sm text-gray-500 mt-1">
                        No libraries were found on your Plex server. 
                        Make sure your server is configured correctly.
                    </p>
                    <div class="mt-4">
                        <button type="button" onclick="syncLibraries()" 
                                class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-plex-600 hover:bg-plex-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-plex-500">
                            <i class="fas fa-sync-alt -ml-1 mr-2 h-4 w-4"></i>
                            Sync Libraries
                        </button>
                    </div>
                </div>
                {% endif %}
            </div>
            
            {% if libraries %}
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="submit" 
                        class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-plex-600 text-base font-medium text-white hover:bg-plex-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-plex-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50"
                        :disabled="loading"
                        :class="{ 'btn-loading': loading }">
                    <span x-show="!loading">Save Settings</span>
                    <span x-show="loading" x-cloak>Saving...</span>
                </button>
                <a href="{{ url_for('admin.settings') }}" 
                   class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-plex-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    Cancel
                </a>
            </div>
            {% endif %}
        </form>
    </div>

    <!-- Help Section -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-info-circle h-5 w-5 text-blue-400"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">
                    How Default Library Sharing Works
                </h3>
                <div class="mt-2 text-sm text-blue-700">
                    <ul class="list-disc list-inside space-y-1">
                        <li>Libraries marked as "Default" will be automatically shared with new users when they get a subscription</li>
                        <li>When a subscription expires, all libraries are unshared but the original configuration is remembered</li>
                        <li>When a subscription is renewed, the original library sharing configuration is restored</li>
                        <li>You can manually share/unshare individual libraries for specific users at any time</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
async function syncLibraries() {
    try {
        const response = await fetch('/admin/api/sync-libraries', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            PlexApp.utils.showNotification(data.message, 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            PlexApp.utils.showNotification('Sync failed: ' + data.message, 'error');
        }
    } catch (error) {
        PlexApp.utils.showNotification('Sync failed: ' + error.message, 'error');
    }
}
</script>
{% endblock %}
