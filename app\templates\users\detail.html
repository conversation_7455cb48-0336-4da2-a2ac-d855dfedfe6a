{% extends "base.html" %}

{% block title %}{{ user.username }} - User Details{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex items-center justify-between mb-8">
        <div class="flex items-center space-x-4">
            <a href="{{ url_for('main.users') }}" class="text-blue-600 hover:text-blue-800">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
            </a>
            <h1 class="text-3xl font-bold text-gray-900">{{ user.username }}</h1>
            <span class="px-3 py-1 text-sm font-medium rounded-full 
                {% if user.get_subscription_status() == 'active' %}bg-green-100 text-green-800
                {% elif user.get_subscription_status() == 'expired' %}bg-red-100 text-red-800
                {% elif user.get_subscription_status() == 'expiring_soon' %}bg-yellow-100 text-yellow-800
                {% else %}bg-gray-100 text-gray-800{% endif %}">
                {{ user.get_subscription_status().replace('_', ' ').title() }}
            </span>
        </div>
        
        <div class="flex space-x-2">
            <button onclick="syncFromPlex()" class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700">
                <i class="fas fa-sync mr-1"></i> Sync from Plex
            </button>
            <button onclick="extendSubscription()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                Extend Subscription
            </button>
            <button onclick="setUnlimitedSubscription()" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700">
                Set Unlimited
            </button>
            <button onclick="cancelSubscription()" class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700">
                Cancel Subscription
            </button>
        </div>
    </div>

    <!-- User Info Card -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">User Information</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700">Username</label>
                <p class="mt-1 text-sm text-gray-900">{{ user.username }}</p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Email</label>
                <p class="mt-1 text-sm text-gray-900">{{ user.email or 'Not provided' }}</p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Plex User ID</label>
                <p class="mt-1 text-sm text-gray-900">{{ user.plex_user_id }}</p>
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Last Sync</label>
                <p class="mt-1 text-sm text-gray-900">
                    {% if user.last_sync %}
                        {{ user.last_sync.strftime('%Y-%m-%d %H:%M') }}
                    {% else %}
                        Never
                    {% endif %}
                </p>
            </div>
        </div>
    </div>

    <!-- Current Subscription Card -->
    {% set active_subscription = user.get_active_subscription() %}
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">Current Subscription</h2>
        {% if active_subscription %}
            <div id="subscription-info">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Plan</label>
                        <p class="mt-1 text-sm text-gray-900">{{ active_subscription.plan_name or 'Standard Plan' }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Start Date</label>
                        <p class="mt-1 text-sm text-gray-900">{{ active_subscription.start_date.strftime('%Y-%m-%d') }}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">End Date</label>
                        <p class="mt-1 text-sm text-gray-900">
                            {% if active_subscription.is_unlimited() %}
                                <span class="text-purple-600 font-semibold">Unlimited</span>
                            {% else %}
                                {{ active_subscription.end_date.strftime('%Y-%m-%d') }}
                            {% endif %}
                        </p>
                    </div>
                </div>
                
                {% if not active_subscription.is_unlimited() %}
                <!-- Countdown Timer -->
                <div class="bg-gray-50 rounded-lg p-4 mb-4">
                    <h3 class="text-lg font-medium mb-2">Time Remaining</h3>
                    <div id="countdown-display" class="text-2xl font-bold text-blue-600 mb-2">
                        Loading...
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div id="progress-bar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                </div>
                {% else %}
                <div class="bg-purple-50 rounded-lg p-4 mb-4">
                    <h3 class="text-lg font-medium text-purple-800 mb-2">Unlimited Subscription</h3>
                    <p class="text-purple-600">This user has unlimited access to all shared libraries.</p>
                </div>
                {% endif %}
            </div>
        {% else %}
            <div class="text-center py-8">
                <p class="text-gray-500 mb-4">No active subscription</p>
                <button onclick="createSubscription()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700">
                    Create Subscription
                </button>
            </div>
        {% endif %}
    </div>

    <!-- Shared Libraries Card -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">Shared Libraries</h2>
        {% if shared_libraries %}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {% for shared_lib in shared_libraries %}
                    <div class="border rounded-lg p-4 {% if shared_lib.is_shared %}border-green-200 bg-green-50{% else %}border-red-200 bg-red-50{% endif %}">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="font-medium">{{ shared_lib.library.title }}</h3>
                                <p class="text-sm text-gray-600">{{ shared_lib.library.type.title() }}</p>
                            </div>
                            <div class="flex items-center space-x-2">
                                {% if shared_lib.is_shared %}
                                    <span class="text-green-600">
                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                        </svg>
                                    </span>
                                    <button onclick="unshareLibrary({{ shared_lib.library.id }})" class="text-red-600 hover:text-red-800 text-sm">
                                        Unshare
                                    </button>
                                {% else %}
                                    <span class="text-red-600">
                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                        </svg>
                                    </span>
                                    <button onclick="shareLibrary({{ shared_lib.library.id }})" class="text-green-600 hover:text-green-800 text-sm">
                                        Share
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <p class="text-gray-500 text-center py-8">No libraries shared with this user</p>
        {% endif %}
    </div>

    <!-- Subscription History Card -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4">Subscription History</h2>
        {% if subscriptions %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Start Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">End Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for subscription in subscriptions %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ subscription.plan_name or 'Standard Plan' }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ subscription.start_date.strftime('%Y-%m-%d') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {% if subscription.is_unlimited() %}
                                        <span class="text-purple-600 font-semibold">Unlimited</span>
                                    {% else %}
                                        {{ subscription.end_date.strftime('%Y-%m-%d') }}
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                        {% if subscription.status == 'active' %}bg-green-100 text-green-800
                                        {% elif subscription.status == 'expired' %}bg-red-100 text-red-800
                                        {% elif subscription.status == 'cancelled' %}bg-gray-100 text-gray-800
                                        {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                                        {{ subscription.status.title() }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {% if subscription.is_unlimited() %}
                                        Unlimited
                                    {% else %}
                                        {{ subscription.plan_duration_days or 'N/A' }} days
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <p class="text-gray-500 text-center py-8">No subscription history</p>
        {% endif %}
    </div>
</div>

<!-- Modals and JavaScript will be added in the next part -->
{% endblock %}

{% block scripts %}
<script>
const userId = {{ user.id }};
let countdownInterval;

// Start countdown timer if subscription is active and not unlimited
{% if active_subscription and not active_subscription.is_unlimited() %}
function updateCountdown() {
    fetch(`/api/subscription-countdown/${userId}`)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'active') {
                document.getElementById('countdown-display').textContent = data.formatted_time_left;
                document.getElementById('progress-bar').style.width = data.progress_percentage + '%';
            } else {
                document.getElementById('countdown-display').textContent = 'Expired';
                document.getElementById('progress-bar').style.width = '100%';
                clearInterval(countdownInterval);
            }
        })
        .catch(error => console.error('Error updating countdown:', error));
}

// Update countdown every minute
countdownInterval = setInterval(updateCountdown, 60000);
updateCountdown(); // Initial update
{% endif %}

// Subscription management functions
function extendSubscription() {
    const days = prompt('Enter number of days to extend:');
    if (days && parseInt(days) > 0) {
        fetch(`/api/users/${userId}/extend-subscription`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ days: parseInt(days) })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred');
        });
    }
}

function setUnlimitedSubscription() {
    if (confirm('Set unlimited subscription for this user?')) {
        fetch(`/api/users/${userId}/set-unlimited-subscription`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ plan_name: 'Unlimited Plan' })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred');
        });
    }
}

function cancelSubscription() {
    if (confirm('Cancel subscription for this user?')) {
        fetch(`/api/users/${userId}/cancel-subscription`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred');
        });
    }
}

function createSubscription() {
    const days = prompt('Enter number of days for new subscription:');
    if (days && parseInt(days) > 0) {
        fetch(`/api/users/${userId}/create-subscription`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ 
                days: parseInt(days),
                plan_name: 'Custom Plan'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred');
        });
    }
}

function shareLibrary(libraryId) {
    fetch(`/api/users/${userId}/share-library`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ library_id: libraryId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.manual_instructions) {
            // Show detailed manual instructions
            let message = data.message + '\n\nManual Instructions:\n';
            data.manual_instructions.instructions.forEach((instruction, index) => {
                message += instruction + '\n';
            });
            alert(message);
        } else {
            alert(data.message);
        }
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred');
    });
}

function unshareLibrary(libraryId) {
    fetch(`/api/users/${userId}/unshare-library`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ library_id: libraryId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.manual_instructions) {
            // Show detailed manual instructions
            let message = data.message + '\n\nManual Instructions:\n';
            data.manual_instructions.instructions.forEach((instruction, index) => {
                message += instruction + '\n';
            });
            alert(message);
        } else {
            alert(data.message);
        }
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred');
    });
}

function syncFromPlex() {
    if (confirm('Sync user data and library sharing status from Plex? This will update the display to match the current Plex settings.')) {
        fetch('/api/sync-plex-data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Successfully synced from Plex: ' + data.message);
                location.reload();
            } else {
                alert('Sync failed: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Sync failed: ' + error.message);
        });
    }
}
</script>
{% endblock %}
