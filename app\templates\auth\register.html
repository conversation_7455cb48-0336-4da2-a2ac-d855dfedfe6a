{% extends "base.html" %}

{% block title %}Register - Plex User Subscription Manager{% endblock %}

{% block auth_content %}
<div class="max-w-md w-full space-y-8">
    <div>
        <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-plex-500">
            <i class="fas fa-user-plus text-white text-xl"></i>
        </div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
            {% if is_first_admin %}Create Master Admin Account{% else %}Create Admin Account{% endif %}
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
            {% if is_first_admin %}
            Set up the first administrator account
            {% else %}
            Add a new administrator to the system
            {% endif %}
        </p>
    </div>
    
    <form class="mt-8 space-y-6" method="POST" x-data="{ loading: false }" @submit="loading = true">
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
        
        <div class="space-y-4">
            <div>
                <label for="username" class="block text-sm font-medium text-gray-700">Username</label>
                <input id="username" name="username" type="text" required 
                       class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-plex-500 focus:border-plex-500 sm:text-sm" 
                       placeholder="Enter username">
            </div>
            
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                <input id="email" name="email" type="email" required 
                       class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-plex-500 focus:border-plex-500 sm:text-sm" 
                       placeholder="Enter email address">
            </div>
            
            <div>
                <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                <input id="password" name="password" type="password" required 
                       class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-plex-500 focus:border-plex-500 sm:text-sm" 
                       placeholder="Enter password">
            </div>
            
            <div>
                <label for="confirm_password" class="block text-sm font-medium text-gray-700">Confirm Password</label>
                <input id="confirm_password" name="confirm_password" type="password" required 
                       class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-plex-500 focus:border-plex-500 sm:text-sm" 
                       placeholder="Confirm password">
            </div>
            
            {% if is_first_admin %}
            <div class="flex items-center">
                <input id="is_master" name="is_master" type="checkbox" checked disabled
                       class="h-4 w-4 text-plex-600 focus:ring-plex-500 border-gray-300 rounded">
                <label for="is_master" class="ml-2 block text-sm text-gray-900">
                    Master Administrator (required for first account)
                </label>
            </div>
            {% endif %}
        </div>

        <div>
            <button type="submit" 
                    class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-plex-600 hover:bg-plex-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-plex-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    :disabled="loading"
                    :class="{ 'btn-loading': loading }">
                <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                    <i class="fas fa-user-plus h-5 w-5 text-plex-500 group-hover:text-plex-400"></i>
                </span>
                <span x-show="!loading">Create Account</span>
                <span x-show="loading" x-cloak>Creating...</span>
            </button>
        </div>

        {% if not is_first_admin %}
        <div class="text-center">
            <p class="text-sm text-gray-600">
                <a href="{{ url_for('main.dashboard') }}" class="font-medium text-plex-600 hover:text-plex-500">
                    Back to Dashboard
                </a>
            </p>
        </div>
        {% endif %}
    </form>
</div>
{% endblock %}
