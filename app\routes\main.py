"""
Main application routes for dashboard and general pages
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from datetime import datetime, timedelta
from app import db
from app.models import User, Subscription, Library, SharedLibrary
from app.services.subscription_service import SubscriptionService

main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def index():
    """Home page - redirect to dashboard if logged in, otherwise to login"""
    if current_user.is_authenticated:
        return redirect(url_for('main.dashboard'))
    return redirect(url_for('auth.login'))

@main_bp.route('/dashboard')
@login_required
def dashboard():
    """Main dashboard showing user subscriptions and statistics"""
    # Get admin's Plex server
    plex_server = current_user.get_plex_server()
    if not plex_server:
        flash('Please configure your Plex server first.', 'warning')
        return redirect(url_for('admin.plex_setup'))
    
    # Get users for this admin's server
    users = User.query.filter_by(plex_server_id=plex_server.id).all()
    
    # Calculate statistics
    total_users = len(users)
    active_users = len([u for u in users if u.get_subscription_status() == 'active'])
    expired_users = len([u for u in users if u.get_subscription_status() == 'expired'])
    expiring_soon = len([u for u in users if u.days_until_expiry() is not None and 0 < u.days_until_expiry() <= 7])
    
    # Get recent activity (subscriptions created/updated in last 30 days)
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    recent_subscriptions = Subscription.query.join(User).filter(
        User.plex_server_id == plex_server.id,
        Subscription.created_at >= thirty_days_ago
    ).order_by(Subscription.created_at.desc()).limit(10).all()
    
    # Get expiring subscriptions
    expiring_subscriptions = []
    for user in users:
        days_left = user.days_until_expiry()
        if days_left is not None and 0 < days_left <= 14:
            expiring_subscriptions.append({
                'user': user,
                'days_left': days_left
            })
    
    # Sort by days left
    expiring_subscriptions.sort(key=lambda x: x['days_left'])
    
    stats = {
        'total_users': total_users,
        'active_users': active_users,
        'expired_users': expired_users,
        'expiring_soon': expiring_soon
    }
    
    return render_template('dashboard/index.html', 
                         users=users, 
                         stats=stats,
                         recent_subscriptions=recent_subscriptions,
                         expiring_subscriptions=expiring_subscriptions)

@main_bp.route('/users')
@login_required
def users():
    """Users management page"""
    plex_server = current_user.get_plex_server()
    if not plex_server:
        flash('Please configure your Plex server first.', 'warning')
        return redirect(url_for('admin.plex_setup'))
    
    # Get pagination parameters
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    search = request.args.get('search', '')
    status_filter = request.args.get('status', 'all')
    
    # Build query
    query = User.query.filter_by(plex_server_id=plex_server.id)
    
    # Apply search filter
    if search:
        query = query.filter(
            db.or_(
                User.username.contains(search),
                User.email.contains(search),
                User.title.contains(search)
            )
        )
    
    # Apply status filter
    if status_filter == 'active':
        query = query.filter(User.subscription_end_date > datetime.utcnow())
    elif status_filter == 'expired':
        query = query.filter(User.subscription_end_date <= datetime.utcnow())
    elif status_filter == 'expiring':
        week_from_now = datetime.utcnow() + timedelta(days=7)
        query = query.filter(
            User.subscription_end_date > datetime.utcnow(),
            User.subscription_end_date <= week_from_now
        )
    
    # Paginate results
    users_pagination = query.order_by(User.username).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('users/index.html', 
                         users=users_pagination.items,
                         pagination=users_pagination,
                         search=search,
                         status_filter=status_filter)

@main_bp.route('/users/<int:user_id>')
@login_required
def user_detail(user_id):
    """User detail page"""
    plex_server = current_user.get_plex_server()
    user = User.query.filter_by(id=user_id, plex_server_id=plex_server.id).first_or_404()
    
    # Get user's subscription history
    subscriptions = user.subscriptions.order_by(Subscription.created_at.desc()).all()
    
    # Get user's shared libraries
    shared_libraries = SharedLibrary.query.filter_by(user_id=user.id).join(Library).all()
    
    # Get available libraries
    available_libraries = Library.query.filter_by(plex_server_id=plex_server.id).all()
    
    return render_template('users/detail.html', 
                         user=user,
                         subscriptions=subscriptions,
                         shared_libraries=shared_libraries,
                         available_libraries=available_libraries)

@main_bp.route('/libraries')
@login_required
def libraries():
    """Libraries management page"""
    plex_server = current_user.get_plex_server()
    if not plex_server:
        flash('Please configure your Plex server first.', 'warning')
        return redirect(url_for('admin.plex_setup'))
    
    libraries = Library.query.filter_by(plex_server_id=plex_server.id).all()
    
    # Calculate sharing statistics for each library
    for library in libraries:
        library.shared_count = library.get_shared_users_count()
        library.total_users = User.query.filter_by(plex_server_id=plex_server.id).count()
    
    return render_template('libraries/index.html', libraries=libraries)

@main_bp.route('/api/dashboard/stats')
@login_required
def api_dashboard_stats():
    """API endpoint for dashboard statistics"""
    plex_server = current_user.get_plex_server()
    if not plex_server:
        return jsonify({'error': 'No Plex server configured'}), 400
    
    users = User.query.filter_by(plex_server_id=plex_server.id).all()
    
    stats = {
        'total_users': len(users),
        'active_users': len([u for u in users if u.get_subscription_status() == 'active']),
        'expired_users': len([u for u in users if u.get_subscription_status() == 'expired']),
        'expiring_soon': len([u for u in users if u.days_until_expiry() is not None and 0 < u.days_until_expiry() <= 7])
    }
    
    return jsonify(stats)

@main_bp.route('/api/users/<int:user_id>/subscription-status')
@login_required
def api_user_subscription_status(user_id):
    """API endpoint for user subscription status"""
    plex_server = current_user.get_plex_server()
    user = User.query.filter_by(id=user_id, plex_server_id=plex_server.id).first_or_404()
    
    status = user.get_subscription_status()
    days_left = user.days_until_expiry()
    
    # Calculate progress percentage
    progress = 0
    if user.subscription_end_date and status == 'active':
        current_subscription = user.subscriptions.filter_by(status='active').first()
        if current_subscription:
            progress = current_subscription.progress_percentage()
    
    return jsonify({
        'status': status,
        'days_left': days_left,
        'progress': progress,
        'end_date': user.subscription_end_date.isoformat() if user.subscription_end_date else None
    })

@main_bp.route('/api/users/expiring')
@login_required
def api_expiring_users():
    """API endpoint for users with expiring subscriptions"""
    plex_server = current_user.get_plex_server()
    if not plex_server:
        return jsonify({'error': 'No Plex server configured'}), 400
    
    days = request.args.get('days', 7, type=int)
    
    users = User.query.filter_by(plex_server_id=plex_server.id).all()
    expiring_users = []
    
    for user in users:
        days_left = user.days_until_expiry()
        if days_left is not None and 0 < days_left <= days:
            expiring_users.append({
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'days_left': days_left,
                'end_date': user.subscription_end_date.isoformat()
            })
    
    # Sort by days left
    expiring_users.sort(key=lambda x: x['days_left'])
    
    return jsonify(expiring_users)

@main_bp.route('/api/sync-plex-data', methods=['POST'])
@login_required
def api_sync_plex_data():
    """API endpoint to sync data from Plex server"""
    plex_server = current_user.get_plex_server()
    if not plex_server:
        return jsonify({'error': 'No Plex server configured'}), 400
    
    try:
        from app.services.plex_service import PlexService
        plex_service = PlexService(plex_server)
        
        # Sync users
        user_success, user_message = plex_service.sync_users_from_plex()
        
        # Sync libraries
        lib_success, lib_message = plex_service.sync_libraries_from_plex()
        
        if user_success and lib_success:
            return jsonify({
                'success': True,
                'message': f'{user_message}. {lib_message}.'
            })
        else:
            return jsonify({
                'success': False,
                'message': f'Sync partially failed. Users: {user_message}. Libraries: {lib_message}.'
            }), 500
            
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Sync failed: {str(e)}'
        }), 500

# Error handlers
@main_bp.errorhandler(404)
def not_found(error):
    """Handle 404 errors"""
    if request.is_json:
        return jsonify({'error': 'Resource not found'}), 404
    return render_template('errors/404.html'), 404

@main_bp.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    db.session.rollback()
    if request.is_json:
        return jsonify({'error': 'Internal server error'}), 500
    return render_template('errors/500.html'), 500
